"""
Migration: Add date sticker configuration fields to channels table
"""

import os
import sys
import logging
from sqlalchemy import text

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from src.database.setup import get_async_db, is_sqlite_db

logger = logging.getLogger(__name__)

async def add_date_sticker_fields():
    """Add date sticker configuration fields to channels table"""
    logger.info("🔄 Adding date sticker fields to channels table...")
    
    async for db in get_async_db():
        try:
            if is_sqlite_db():
                # SQLite migrations
                logger.info("📊 Running SQLite migration for date sticker fields")
                
                # Check if columns already exist
                result = db.execute(text("PRAGMA table_info(channels)"))
                columns = [row[1] for row in result.fetchall()]
                
                if 'enable_date_stickers' not in columns:
                    db.execute(text("ALTER TABLE channels ADD COLUMN enable_date_stickers BOOLEAN DEFAULT 1"))
                    logger.info("✅ Added enable_date_stickers column")
                else:
                    logger.info("ℹ️ enable_date_stickers column already exists")
                
                if 'date_sticker_style' not in columns:
                    db.execute(text("ALTER TABLE channels ADD COLUMN date_sticker_style VARCHAR(50) DEFAULT 'modern'"))
                    logger.info("✅ Added date_sticker_style column")
                else:
                    logger.info("ℹ️ date_sticker_style column already exists")
                
                db.commit()
                
            else:
                # PostgreSQL migrations
                logger.info("🐘 Running PostgreSQL migration for date sticker fields")
                
                # Check if columns already exist
                result = await db.execute(text("""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = 'channels'
                """))
                columns = [row[0] for row in result.fetchall()]
                
                if 'enable_date_stickers' not in columns:
                    await db.execute(text("ALTER TABLE channels ADD COLUMN enable_date_stickers BOOLEAN DEFAULT true"))
                    logger.info("✅ Added enable_date_stickers column")
                else:
                    logger.info("ℹ️ enable_date_stickers column already exists")
                
                if 'date_sticker_style' not in columns:
                    await db.execute(text("ALTER TABLE channels ADD COLUMN date_sticker_style VARCHAR(50) DEFAULT 'modern'"))
                    logger.info("✅ Added date_sticker_style column")
                else:
                    logger.info("ℹ️ date_sticker_style column already exists")
                
                await db.commit()
            
            logger.info("✅ Date sticker fields migration completed successfully")
            
        except Exception as e:
            logger.error(f"❌ Error adding date sticker fields: {e}", exc_info=True)
            if is_sqlite_db():
                db.rollback()
            else:
                await db.rollback()
            raise

if __name__ == "__main__":
    import asyncio
    asyncio.run(add_date_sticker_fields())
