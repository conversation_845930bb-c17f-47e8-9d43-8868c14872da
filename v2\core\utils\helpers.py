"""
Utility helper functions for MignalyBot v2

Provides common utility functions with performance optimizations:
- Time and timezone handling
- Data validation
- Text processing
- Performance monitoring
- Memory management
"""

import asyncio
import gc
import hashlib
import time
import psutil
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Union, Callable
from functools import wraps
import pytz


def get_current_time(tz: str = "UTC") -> datetime:
    """
    Get current time in specified timezone
    
    Args:
        tz: Timezone string (default: UTC)
        
    Returns:
        Current datetime in specified timezone
    """
    if tz == "UTC":
        return datetime.now(timezone.utc)
    
    try:
        timezone_obj = pytz.timezone(tz)
        return datetime.now(timezone_obj)
    except pytz.UnknownTimeZoneError:
        # Fallback to UTC
        return datetime.now(timezone.utc)


def convert_timezone(dt: datetime, from_tz: str, to_tz: str) -> datetime:
    """
    Convert datetime from one timezone to another
    
    Args:
        dt: Datetime object
        from_tz: Source timezone
        to_tz: Target timezone
        
    Returns:
        Converted datetime
    """
    try:
        if dt.tzinfo is None:
            # Assume source timezone
            source_tz = pytz.timezone(from_tz)
            dt = source_tz.localize(dt)
        
        target_tz = pytz.timezone(to_tz)
        return dt.astimezone(target_tz)
    except pytz.UnknownTimeZoneError:
        return dt


def validate_symbol(symbol: str) -> bool:
    """
    Validate trading symbol format
    
    Args:
        symbol: Trading symbol (e.g., BTC/USD)
        
    Returns:
        True if valid, False otherwise
    """
    if not symbol or not isinstance(symbol, str):
        return False
    
    # Basic validation for common formats
    if "/" in symbol:
        parts = symbol.split("/")
        return len(parts) == 2 and all(len(part) >= 2 for part in parts)
    
    return len(symbol) >= 3


def validate_timeframe(timeframe: str) -> bool:
    """
    Validate timeframe format
    
    Args:
        timeframe: Timeframe string (e.g., 1h, 4h, 1d)
        
    Returns:
        True if valid, False otherwise
    """
    valid_timeframes = {
        "1m", "5m", "15m", "30m",
        "1h", "2h", "4h", "6h", "8h", "12h",
        "1d", "3d", "1w", "1M"
    }
    return timeframe in valid_timeframes


def chunk_text(text: str, max_length: int = 4000, preserve_words: bool = True) -> List[str]:
    """
    Split text into chunks for Telegram messages
    
    Args:
        text: Text to split
        max_length: Maximum length per chunk
        preserve_words: Whether to preserve word boundaries
        
    Returns:
        List of text chunks
    """
    if len(text) <= max_length:
        return [text]
    
    chunks = []
    current_chunk = ""
    
    if preserve_words:
        words = text.split()
        for word in words:
            if len(current_chunk) + len(word) + 1 <= max_length:
                current_chunk += (" " if current_chunk else "") + word
            else:
                if current_chunk:
                    chunks.append(current_chunk)
                current_chunk = word
        
        if current_chunk:
            chunks.append(current_chunk)
    else:
        for i in range(0, len(text), max_length):
            chunks.append(text[i:i + max_length])
    
    return chunks


def generate_hash(data: Union[str, Dict, List]) -> str:
    """
    Generate MD5 hash for data
    
    Args:
        data: Data to hash
        
    Returns:
        MD5 hash string
    """
    if isinstance(data, (dict, list)):
        data = str(sorted(data.items()) if isinstance(data, dict) else data)
    
    return hashlib.md5(str(data).encode()).hexdigest()


def safe_float(value: Any, default: float = 0.0) -> float:
    """
    Safely convert value to float
    
    Args:
        value: Value to convert
        default: Default value if conversion fails
        
    Returns:
        Float value or default
    """
    try:
        return float(value)
    except (ValueError, TypeError):
        return default


def safe_int(value: Any, default: int = 0) -> int:
    """
    Safely convert value to int
    
    Args:
        value: Value to convert
        default: Default value if conversion fails
        
    Returns:
        Integer value or default
    """
    try:
        return int(value)
    except (ValueError, TypeError):
        return default


def get_memory_usage() -> float:
    """
    Get current memory usage in MB
    
    Returns:
        Memory usage in MB
    """
    try:
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024
    except Exception:
        return 0.0


def get_cpu_usage() -> float:
    """
    Get current CPU usage percentage
    
    Returns:
        CPU usage percentage
    """
    try:
        return psutil.cpu_percent(interval=1)
    except Exception:
        return 0.0


def force_garbage_collection():
    """Force garbage collection to free memory"""
    gc.collect()


class PerformanceMonitor:
    """Context manager for monitoring performance"""
    
    def __init__(self, operation: str, logger=None):
        self.operation = operation
        self.logger = logger
        self.start_time = None
        self.start_memory = None
    
    def __enter__(self):
        self.start_time = time.time()
        self.start_memory = get_memory_usage()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = time.time() - self.start_time
        memory_used = get_memory_usage() - self.start_memory
        
        if self.logger:
            self.logger.info(
                f"Performance: {self.operation} - Duration: {duration*1000:.2f}ms, "
                f"Memory: {memory_used:.2f}MB",
                extra={
                    'performance': True,
                    'operation': self.operation,
                    'duration': duration,
                    'memory_mb': memory_used
                }
            )


def performance_monitor(operation: str):
    """Decorator for monitoring function performance"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            import logging
            logger = logging.getLogger("mignalybot_v2")
            
            with PerformanceMonitor(f"{func.__name__}:{operation}", logger):
                return func(*args, **kwargs)
        return wrapper
    return decorator


def async_performance_monitor(operation: str):
    """Decorator for monitoring async function performance"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            import logging
            logger = logging.getLogger("mignalybot_v2")
            
            with PerformanceMonitor(f"{func.__name__}:{operation}", logger):
                return await func(*args, **kwargs)
        return wrapper
    return decorator


class RateLimiter:
    """Simple rate limiter for API calls"""
    
    def __init__(self, max_calls: int, window_seconds: int):
        self.max_calls = max_calls
        self.window_seconds = window_seconds
        self.calls = []
    
    async def acquire(self) -> bool:
        """
        Acquire rate limit permission
        
        Returns:
            True if allowed, False if rate limited
        """
        now = time.time()
        
        # Remove old calls outside the window
        self.calls = [call_time for call_time in self.calls if now - call_time < self.window_seconds]
        
        # Check if we can make another call
        if len(self.calls) < self.max_calls:
            self.calls.append(now)
            return True
        
        return False
    
    async def wait_for_slot(self):
        """Wait until a rate limit slot is available"""
        while not await self.acquire():
            await asyncio.sleep(0.1)


class BatchProcessor:
    """Utility for processing items in batches"""
    
    def __init__(self, batch_size: int = 100):
        self.batch_size = batch_size
    
    async def process_batches(
        self,
        items: List[Any],
        processor: Callable,
        *args,
        **kwargs
    ) -> List[Any]:
        """
        Process items in batches
        
        Args:
            items: Items to process
            processor: Processing function
            *args: Additional arguments for processor
            **kwargs: Additional keyword arguments for processor
            
        Returns:
            List of processed results
        """
        results = []
        
        for i in range(0, len(items), self.batch_size):
            batch = items[i:i + self.batch_size]
            
            if asyncio.iscoroutinefunction(processor):
                batch_results = await processor(batch, *args, **kwargs)
            else:
                batch_results = processor(batch, *args, **kwargs)
            
            if isinstance(batch_results, list):
                results.extend(batch_results)
            else:
                results.append(batch_results)
        
        return results


def retry_with_backoff(
    max_retries: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    exponential: bool = True
):
    """
    Decorator for retrying functions with exponential backoff
    
    Args:
        max_retries: Maximum number of retries
        base_delay: Base delay in seconds
        max_delay: Maximum delay in seconds
        exponential: Whether to use exponential backoff
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    if asyncio.iscoroutinefunction(func):
                        return await func(*args, **kwargs)
                    else:
                        return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    
                    if attempt < max_retries:
                        if exponential:
                            delay = min(base_delay * (2 ** attempt), max_delay)
                        else:
                            delay = base_delay
                        
                        await asyncio.sleep(delay)
                    else:
                        raise last_exception
            
            raise last_exception
        return wrapper
    return decorator
