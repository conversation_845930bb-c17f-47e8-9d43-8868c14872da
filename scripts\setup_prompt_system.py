#!/usr/bin/env python3
"""
Complete setup script for MignalyBot Prompt Management System
This script will:
1. Create the prompt_templates table
2. Populate it with unified prompts for all languages
3. Verify the system is working correctly
"""

import asyncio
import logging
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.database.setup import get_async_db, is_sqlite_db
from sqlalchemy import text

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Unified prompt templates that work for ALL languages
# These are based on the current Farsi prompts but designed to work universally
UNIFIED_PROMPTS = {
    "signals": """Create a professional trading signal post in this exact format:

Signal Details:
Symbol: {symbol}
Direction: {direction}
Entry Price: {entry_price}
Stop Loss: {stop_loss}
Take Profit: {take_profit}
Notes: {notes}
Channel Brand: {channel_brand}

Create the signal in this EXACT format (translate to {language} but keep the structure):

🚨 Exclusive Signal for Channel Members 🚨

💎 Symbol: {symbol}

📈 {direction}

🌩 Leverage: 10X

💵 Entry Capital: 5%

📍 Entry Point: {entry_price}

💵 Targets:
💰 First Target: {take_profit}

😀 Stop Loss: {stop_loss}

⚠️ Capital management and risk control is the first step to success, please observe

🔗@{channel_handle}

IMPORTANT:
- Use ONLY the format shown above
- NO greetings or conversational text
- Keep it professional and direct
- Use appropriate emojis as shown
- Include leverage and risk management advice
- Language: {language}
- add #signal #{channel_handle} #{symbol} #{timeframe}
- the output should be in Farsi""",

    "news": """You are a professional financial content creator for "{channel_brand}".
Create an ORIGINAL trading news post tailored specifically for this channel's audience.

News Information:
Title: {title}
Content: {content}
Source: {source}
Language: {language}
Channel Brand: {channel_brand}

IMPORTANT: Create UNIQUE content for this specific channel. Do NOT copy from other channels or use templates.

FORMAT STRUCTURE (create original content following this pattern):

[Write a compelling, original headline about this news with relevant emoji at the end]

[Write 2-3 original sentences analyzing the market impact, include specific data/prices from the news, place ONE relevant emoji in the middle of this paragraph]

🔔 Stay with us

📱 @{channel_handle}

CONTENT REQUIREMENTS:
- Create ORIGINAL headline that captures the key market impact
- Write UNIQUE analysis focusing on trading implications
- Include specific numbers, percentages, or price levels from the news
- Tailor the tone and focus to "{channel_brand}" brand
- Use relevant emojis: 🛢️💰💵💶₿📊📈📉🏦⚡🌍
- Language: {language}
- Be concise but informative (2-3 sentences max in main content)

Remember: This content represents "{channel_brand}" - make it unique and valuable for their specific audience.""",

    "events": """You are a professional economic analyst for "{channel_brand}".
Create an ORIGINAL economic event analysis tailored specifically for this channel's trading audience.

Event Information:
Title: {title}
Country: {country}
Currency: {currency}
Previous: {previous}
Forecast: {forecast}
Actual: {actual}
Impact Level: {impact_level}
Language: {language}
Channel Brand: {channel_brand}

IMPORTANT: Create UNIQUE analysis for this specific channel. Do NOT use generic templates.

FORMAT STRUCTURE (create original content following this pattern):

{country_flag} [Write original event title] {relevant_emoji}

[Write 2-3 original sentences analyzing this event's market impact. Include specific data comparison (Previous vs Forecast vs Actual if available). Explain currency implications and trading opportunities. Place ONE relevant emoji in the middle of this analysis.]

🔔 Stay with us

📱 @{channel_handle}

ANALYSIS REQUIREMENTS:
- Write ORIGINAL 2-3 sentence analysis (not just 6 words)
- Compare actual vs forecast vs previous values when available
- Explain specific impact on {currency} and related markets
- Discuss trading implications and market sentiment
- Include specific numbers and percentages from the event data
- Tailor analysis tone to "{channel_brand}" audience
- Use relevant emojis: 👥📈🏭🏦🌍🛒📊💰⚡
- Language: {language}

Remember: This analysis represents "{channel_brand}" expertise - make it insightful and valuable for traders.""",

    "greeting": """Create a daily greeting message for a trading channel that includes all today's economic events.

Date: {today_date}
Time: {time_greeting}
Total Events: {total_events}
Channel: {channel_brand}
Main Currency: {main_currency}

Events Summary: {events_summary}

Message Structure:

{time_greeting} Dear Traders! 📈
📅 Economic Calendar {formatted_date}
[List ALL events - each on one line with emoji and time]
💡 Today focus on {main_currency} news, market might be volatile
🚀 Be ready and trade smartly!

Rules:
- Include ALL events from the summary above
- Event format: [Impact emoji] [Event name] ([Currency]) - [Hour:Minute]
- Use 🔴 for high impact, 🟡 for medium, 🟢 for low
- Time format: Hour:Minute (24-hour)
- Order by impact (high first), then by time
- Language: {language}
- Keep event names short but clear
- Don't limit to 3-4 events - show all
- Don't use "+X other events" - list everything
- Translate appropriately but keep structure""",

    "market_analysis": """Generate a very short market analysis (max 4 lines) for {symbol} {timeframe}.

Current: {current_price} ({change_str})

Format:
📊 {symbol} {timeframe} | {current_price}
📈 [Trend direction in 2-3 words]
🎯 [Key level or target]
💡 [Brief outlook]

Language: {language}
Keep under 200 characters total. NO greetings or explanations.""",

    "countdown": """Create a countdown post with detailed analysis for "{channel_brand}".

Event Information:
Title: {title}
Country: {country}
Currency: {currency}
Previous: {previous}
Forecast: {forecast}
Minutes until: {minutes_until}
Language: {language}
Channel Brand: {channel_brand}

FORMAT STRUCTURE:
⏰ {country_flag} [Event Title] in {minutes_until}min {impact_emoji}

📊 Previous: {previous} | Forecast: {forecast}

[Write 2-3 sentences analyzing the expected market impact of this event. Explain what traders should watch for, potential currency movements, and market implications. Include ONE relevant emoji in the middle.]

📱 @{channel_handle}

ANALYSIS REQUIREMENTS:
- Write 2-3 sentences of meaningful analysis (not just 6 words)
- Explain what to expect from this event based on forecast vs previous
- Discuss potential {currency} impact and market movements
- Mention key levels or scenarios traders should watch
- Tailor analysis to "{channel_brand}" trading audience
- Language: {language}
- Keep it concise but informative

Remember: Provide valuable pre-event analysis that helps traders prepare.""",

    "signal_update": """Create a very short trade result post following crypto signal style:

Signal: {symbol}
Direction: {direction}
Entry: {entry_price}
Exit: {exit_price}
Result: {profit_loss}
Status: {status}
Channel Brand: {channel_brand}

EXACT format:
{result_emoji} TRADE RESULT {result_emoji}
➖➖➖➖➖➖➖➖➖➖➖➖➖
COIN: {symbol} 🐃
{direction} | {profit_loss}

📍 ENTRY: {entry_price}
📍 EXIT: {exit_price}

CHANNEL ID @{channel_handle} ✅

RULES:
- Use ONLY the format above
- NO greetings or extra text
- Keep it professional and direct
- Maximum 4 digits for prices
- Language: {language}""",

    "event_result": """You are a professional economic analyst for "{channel_brand}".
Create an IMMEDIATE REACTION analysis for this just-released economic data.

Event Information:
Title: {title}
Country: {country}
Currency: {currency}
Previous: {previous}
Forecast: {forecast}
ACTUAL: {actual} ⚡ JUST RELEASED
Impact Level: {impact_level}
Language: {language}

FORMAT STRUCTURE:
🚨 {country_flag} [Event Title] RELEASED {impact_emoji}

📊 Actual: {actual} | Forecast: {forecast} | Previous: {previous}

[Write 2-3 sentences analyzing the immediate market impact. Compare actual vs forecast. Explain what this means for {currency} and trading opportunities.]

⚡ Live Analysis
📱 @{channel_handle}

REQUIREMENTS:
- Focus on IMMEDIATE market reaction
- Compare actual vs forecast vs previous
- Explain currency impact and trading implications
- Keep under 200 characters total
- Language: {language}"""
}

async def create_prompt_templates_table():
    """Create the prompt_templates table"""
    logger.info("🔧 Creating prompt_templates table...")
    
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS prompt_templates (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        post_type VARCHAR(50) NOT NULL,
        language VARCHAR(10) NOT NULL DEFAULT 'fa',
        template_content TEXT NOT NULL,
        description TEXT,
        active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(post_type, language)
    );
    """
    
    try:
        async for db in get_async_db():
            if is_sqlite_db():
                db.execute(text(create_table_sql))
                db.commit()
            else:
                await db.execute(text(create_table_sql))
                await db.commit()
            logger.info("✅ Created prompt_templates table")
            break
    except Exception as e:
        logger.error(f"❌ Error creating prompt_templates table: {e}")
        raise

async def populate_unified_prompts():
    """Populate the table with unified prompts for all languages"""
    logger.info("📝 Populating unified prompts...")
    
    languages = [
        ('fa', 'Farsi'),
        ('en', 'English'), 
        ('ar', 'Arabic')
    ]
    
    try:
        async for db in get_async_db():
            for post_type, template_content in UNIFIED_PROMPTS.items():
                for lang_code, lang_name in languages:
                    # Check if prompt already exists
                    check_sql = "SELECT id FROM prompt_templates WHERE post_type = :post_type AND language = :language"
                    
                    if is_sqlite_db():
                        result = db.execute(text(check_sql), {"post_type": post_type, "language": lang_code})
                        existing = result.fetchone()
                    else:
                        result = await db.execute(text(check_sql), {"post_type": post_type, "language": lang_code})
                        existing = result.fetchone()
                    
                    if not existing:
                        insert_sql = """
                        INSERT INTO prompt_templates (post_type, language, template_content, description, active)
                        VALUES (:post_type, :language, :template_content, :description, 1)
                        """
                        description = f"Unified {lang_name} template for {post_type} posts"
                        
                        params = {
                            "post_type": post_type,
                            "language": lang_code,
                            "template_content": template_content,
                            "description": description
                        }
                        
                        if is_sqlite_db():
                            db.execute(text(insert_sql), params)
                        else:
                            await db.execute(text(insert_sql), params)
                        
                        logger.info(f"✅ Added unified prompt for {post_type} ({lang_code})")
            
            if is_sqlite_db():
                db.commit()
            else:
                await db.commit()
                
            logger.info("✅ Populated unified prompts")
            break
    except Exception as e:
        logger.error(f"❌ Error populating unified prompts: {e}")
        raise

async def verify_system():
    """Verify the prompt system is working correctly"""
    logger.info("🔍 Verifying prompt system...")
    
    try:
        async for db in get_async_db():
            if is_sqlite_db():
                result = db.execute(text("SELECT COUNT(*) FROM prompt_templates"))
                count = result.fetchone()[0]
                
                result = db.execute(text("SELECT post_type, language FROM prompt_templates ORDER BY post_type, language"))
                templates = result.fetchall()
            else:
                result = await db.execute(text("SELECT COUNT(*) FROM prompt_templates"))
                count = result.fetchone()[0]
                
                result = await db.execute(text("SELECT post_type, language FROM prompt_templates ORDER BY post_type, language"))
                templates = result.fetchall()
            
            logger.info(f"📊 Total prompt templates: {count}")
            
            if count > 0:
                logger.info("📋 Available templates:")
                for template in templates:
                    logger.info(f"   - {template[0]} ({template[1]})")
            
            # Test loading a prompt
            from src.ai_integration.qwen_client import QwenClient
            client = QwenClient()
            
            test_prompt = await client.get_prompt_template("signals", "fa")
            if test_prompt:
                logger.info("✅ Prompt loading test: SUCCESS")
                logger.info(f"   Preview: {test_prompt[:100]}...")
            else:
                logger.warning("⚠️ Prompt loading test: FAILED")
            
            break
    except Exception as e:
        logger.error(f"❌ Error verifying system: {e}")
        raise

async def main():
    """Main setup function"""
    logger.info("🚀 Starting MignalyBot Prompt System Setup")
    logger.info("=" * 50)
    
    try:
        # Step 1: Create table
        await create_prompt_templates_table()
        
        # Step 2: Populate with unified prompts
        await populate_unified_prompts()
        
        # Step 3: Verify system
        await verify_system()
        
        logger.info("=" * 50)
        logger.info("🎉 Prompt system setup completed successfully!")
        logger.info("")
        logger.info("Next steps:")
        logger.info("1. Restart MignalyBot application")
        logger.info("2. Access admin interface at: http://localhost:8000/prompts")
        logger.info("3. Edit prompt templates as needed")
        logger.info("4. Test content generation")
        
    except Exception as e:
        logger.error(f"💥 Setup failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
