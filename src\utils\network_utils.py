"""
Network utilities for MignalyBot
Provides centralized network configuration, retry logic, and error handling
"""

import asyncio
import logging
import httpx
from typing import Optional, Dict, Any, Callable
from functools import wraps

logger = logging.getLogger(__name__)

# Default network configurations
DEFAULT_TIMEOUTS = {
    'connect': 10.0,
    'read': 30.0,
    'write': 20.0,
    'pool': 5.0
}

DEFAULT_LIMITS = {
    'max_connections': 10,
    'max_keepalive_connections': 5
}

DEFAULT_RETRY_CONFIG = {
    'max_retries': 5,
    'base_delay': 1.0,
    'max_delay': 300.0,
    'exponential_base': 2.0
}

class NetworkError(Exception):
    """Custom network error for better error handling"""
    def __init__(self, message: str, error_type: str = "NetworkError", original_error: Exception = None):
        super().__init__(message)
        self.error_type = error_type
        self.original_error = original_error

def create_http_client(
    timeout_config: Optional[Dict[str, float]] = None,
    limits_config: Optional[Dict[str, int]] = None,
    **kwargs
) -> httpx.AsyncClient:
    """
    Create an HTTP client with optimized settings for reliability
    
    Args:
        timeout_config: Custom timeout configuration
        limits_config: Custom connection limits configuration
        **kwargs: Additional httpx.AsyncClient arguments
        
    Returns:
        Configured httpx.AsyncClient
    """
    # Use default configurations if not provided
    timeouts = timeout_config or DEFAULT_TIMEOUTS
    limits = limits_config or DEFAULT_LIMITS
    
    # Create timeout configuration
    timeout = httpx.Timeout(
        connect=timeouts.get('connect', DEFAULT_TIMEOUTS['connect']),
        read=timeouts.get('read', DEFAULT_TIMEOUTS['read']),
        write=timeouts.get('write', DEFAULT_TIMEOUTS['write']),
        pool=timeouts.get('pool', DEFAULT_TIMEOUTS['pool'])
    )
    
    # Create connection limits
    connection_limits = httpx.Limits(
        max_connections=limits.get('max_connections', DEFAULT_LIMITS['max_connections']),
        max_keepalive_connections=limits.get('max_keepalive_connections', DEFAULT_LIMITS['max_keepalive_connections'])
    )
    
    # Default client configuration
    client_config = {
        'timeout': timeout,
        'limits': connection_limits,
        'follow_redirects': True,
        'verify': True,
        **kwargs
    }
    
    return httpx.AsyncClient(**client_config)

async def retry_with_backoff(
    func: Callable,
    retry_config: Optional[Dict[str, Any]] = None,
    error_handler: Optional[Callable] = None,
    *args,
    **kwargs
) -> Any:
    """
    Execute a function with exponential backoff retry logic
    
    Args:
        func: Async function to execute
        retry_config: Retry configuration
        error_handler: Optional error handler function
        *args: Arguments for the function
        **kwargs: Keyword arguments for the function
        
    Returns:
        Function result
        
    Raises:
        NetworkError: If all retries fail
    """
    config = retry_config or DEFAULT_RETRY_CONFIG
    max_retries = config.get('max_retries', DEFAULT_RETRY_CONFIG['max_retries'])
    base_delay = config.get('base_delay', DEFAULT_RETRY_CONFIG['base_delay'])
    max_delay = config.get('max_delay', DEFAULT_RETRY_CONFIG['max_delay'])
    exponential_base = config.get('exponential_base', DEFAULT_RETRY_CONFIG['exponential_base'])
    
    last_error = None
    
    for attempt in range(max_retries):
        try:
            result = await func(*args, **kwargs)
            if attempt > 0:
                logger.info(f"✅ Operation succeeded on attempt {attempt + 1}")
            return result
            
        except (httpx.RequestError, httpx.TimeoutException, httpx.ConnectError, httpx.ReadError) as e:
            last_error = e
            error_type = type(e).__name__
            error_message = str(e).lower()
            
            logger.warning(f"🌐 Network error on attempt {attempt + 1}/{max_retries}: {error_type}: {e}")
            
            # Calculate backoff delay based on error type
            if "dns" in error_message or "name resolution" in error_message:
                delay = min(base_delay * (exponential_base ** attempt) * 3, max_delay)
                logger.warning("🌐 DNS resolution issue detected - using longer backoff")
            elif "read" in error_message or "ReadError" in error_type:
                delay = min(base_delay * (exponential_base ** attempt) * 2, max_delay)
                logger.warning("📖 Read error detected - connection interrupted")
            elif "connect" in error_message or "ConnectError" in error_type:
                delay = min(base_delay * (exponential_base ** attempt) * 1.5, max_delay)
                logger.warning("🔌 Connection error detected")
            elif "timeout" in error_message or "TimeoutException" in error_type:
                delay = min(base_delay * (exponential_base ** attempt), max_delay)
                logger.warning("⏰ Timeout error detected")
            else:
                delay = min(base_delay * (exponential_base ** attempt), max_delay)
                logger.warning(f"🌐 General network error: {error_type}")
            
            # Call error handler if provided
            if error_handler:
                try:
                    error_handler(e, attempt, max_retries)
                except Exception as handler_error:
                    logger.warning(f"Error handler failed: {handler_error}")
            
            # Don't wait on the last attempt
            if attempt < max_retries - 1:
                logger.info(f"⏳ Waiting {delay:.1f} seconds before retry...")
                await asyncio.sleep(delay)
            
        except Exception as e:
            last_error = e
            logger.error(f"❌ Unexpected error on attempt {attempt + 1}: {e}")
            
            if attempt < max_retries - 1:
                delay = min(base_delay * (exponential_base ** attempt), max_delay)
                logger.info(f"⏳ Waiting {delay:.1f} seconds before retry...")
                await asyncio.sleep(delay)
    
    # All retries failed
    error_msg = f"All {max_retries} attempts failed. Last error: {last_error}"
    logger.error(f"❌ {error_msg}")
    
    if isinstance(last_error, (httpx.RequestError, httpx.TimeoutException, httpx.ConnectError, httpx.ReadError)):
        raise NetworkError(error_msg, type(last_error).__name__, last_error)
    else:
        raise NetworkError(error_msg, "UnknownError", last_error)

def network_resilient(
    retry_config: Optional[Dict[str, Any]] = None,
    error_handler: Optional[Callable] = None
):
    """
    Decorator to add network resilience to async functions
    
    Args:
        retry_config: Retry configuration
        error_handler: Optional error handler function
        
    Returns:
        Decorated function with retry logic
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            return await retry_with_backoff(
                func,
                retry_config=retry_config,
                error_handler=error_handler,
                *args,
                **kwargs
            )
        return wrapper
    return decorator

def log_network_stats(client: httpx.AsyncClient, operation: str = "HTTP operation"):
    """
    Log network statistics for debugging
    
    Args:
        client: HTTP client to inspect
        operation: Description of the operation
    """
    if hasattr(client, '_pool'):
        pool = client._pool
        logger.debug(f"📊 {operation} - Pool stats:")
        logger.debug(f"   Active connections: {len(getattr(pool, '_pool', []))}")
        logger.debug(f"   Keepalive connections: {len(getattr(pool, '_keepalive', []))}")

async def test_network_connectivity(test_urls: Optional[list] = None) -> Dict[str, bool]:
    """
    Test network connectivity to various endpoints
    
    Args:
        test_urls: List of URLs to test (defaults to common services)
        
    Returns:
        Dictionary mapping URLs to connectivity status
    """
    if test_urls is None:
        test_urls = [
            "https://www.google.com",
            "https://api.telegram.org",
            "https://nfs.faireconomy.media",
            "https://www.forexfactory.com"
        ]
    
    results = {}
    
    async with create_http_client() as client:
        for url in test_urls:
            try:
                response = await client.get(url, timeout=10.0)
                results[url] = response.status_code < 400
                logger.debug(f"✅ {url}: {response.status_code}")
            except Exception as e:
                results[url] = False
                logger.debug(f"❌ {url}: {e}")
    
    return results
