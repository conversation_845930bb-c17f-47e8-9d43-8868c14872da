"""
Migration script to add multi-level take profit support
"""

import logging
from sqlalchemy import text, inspect
from src.database.setup import get_async_db, is_sqlite_db

logger = logging.getLogger(__name__)

async def migrate_multi_tp_support():
    """Add multi-level take profit support to the database"""
    logger.info("Starting multi-TP migration...")

    async for db in get_async_db():
        try:
            # Check if migration is needed
            inspector = inspect(db.bind)

            # Check trading_signals table
            ts_columns = [col['name'] for col in inspector.get_columns('trading_signals')]
            ts_migration_needed = 'take_profit_1' not in ts_columns

            # Check posts table
            posts_columns = [col['name'] for col in inspector.get_columns('posts')]
            posts_migration_needed = 'reply_to_message_id' not in posts_columns

            # Check if take_profit_hits table exists
            tables = inspector.get_table_names()
            tp_hits_table_needed = 'take_profit_hits' not in tables

            if not ts_migration_needed and not posts_migration_needed and not tp_hits_table_needed:
                logger.info("Multi-TP migration already applied")
                return

            logger.info(f"Migration status: TS={ts_migration_needed}, Posts={posts_migration_needed}, TPHits={tp_hits_table_needed}")

            logger.info("Applying multi-TP migration...")

            if is_sqlite_db():
                # SQLite migration
                migrate_sqlite(db, ts_migration_needed, posts_migration_needed, tp_hits_table_needed)
            else:
                # PostgreSQL migration
                await migrate_postgresql(db, ts_migration_needed, posts_migration_needed, tp_hits_table_needed)

            logger.info("Multi-TP migration completed successfully")

        except Exception as e:
            logger.error(f"Error during multi-TP migration: {e}", exc_info=True)
            if is_sqlite_db():
                db.rollback()
            else:
                await db.rollback()
            raise

def migrate_sqlite(db, ts_migration_needed, posts_migration_needed, tp_hits_table_needed):
    """SQLite-specific migration"""

    if ts_migration_needed:
        # Step 1: Add new columns to trading_signals
        logger.info("Adding new columns to trading_signals table...")

        try:
            db.execute(text("ALTER TABLE trading_signals ADD COLUMN take_profit_1 REAL;"))
        except Exception as e:
            if "duplicate column name" not in str(e).lower():
                raise

        try:
            db.execute(text("ALTER TABLE trading_signals ADD COLUMN take_profit_2 REAL;"))
        except Exception as e:
            if "duplicate column name" not in str(e).lower():
                raise

        try:
            db.execute(text("ALTER TABLE trading_signals ADD COLUMN take_profit_3 REAL;"))
        except Exception as e:
            if "duplicate column name" not in str(e).lower():
                raise

        try:
            db.execute(text("ALTER TABLE trading_signals ADD COLUMN initial_position_size REAL DEFAULT 100.0;"))
        except Exception as e:
            if "duplicate column name" not in str(e).lower():
                raise

        try:
            db.execute(text("ALTER TABLE trading_signals ADD COLUMN remaining_position_size REAL DEFAULT 100.0;"))
        except Exception as e:
            if "duplicate column name" not in str(e).lower():
                raise

        try:
            db.execute(text("ALTER TABLE trading_signals ADD COLUMN break_even_triggered BOOLEAN DEFAULT 0;"))
        except Exception as e:
            if "duplicate column name" not in str(e).lower():
                raise

        # Step 2: Migrate existing data
        logger.info("Migrating existing signal data...")

        db.execute(text("""
            UPDATE trading_signals
            SET take_profit_1 = take_profit
            WHERE take_profit_1 IS NULL AND take_profit IS NOT NULL;
        """))

    if posts_migration_needed:
        # Step 3: Add new columns to posts table
        logger.info("Adding new columns to posts table...")

        try:
            db.execute(text("ALTER TABLE posts ADD COLUMN reply_to_message_id VARCHAR(50);"))
        except Exception as e:
            if "duplicate column name" not in str(e).lower():
                raise

        try:
            db.execute(text("ALTER TABLE posts ADD COLUMN tp_level INTEGER;"))
        except Exception as e:
            if "duplicate column name" not in str(e).lower():
                raise

    if tp_hits_table_needed:
        # Step 4: Create take_profit_hits table
        logger.info("Creating take_profit_hits table...")

        try:
            db.execute(text("""
                CREATE TABLE take_profit_hits (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    signal_id INTEGER NOT NULL,
                    tp_level INTEGER NOT NULL,
                    tp_price REAL NOT NULL,
                    hit_time DATETIME NOT NULL,
                    hit_price REAL NOT NULL,
                    position_closed_percentage REAL NOT NULL,
                    profit_loss_percentage REAL NOT NULL,
                    created_at DATETIME NOT NULL,
                    FOREIGN KEY (signal_id) REFERENCES trading_signals(id)
                );
            """))
        except Exception as e:
            if "already exists" not in str(e).lower():
                raise

    db.commit()

async def migrate_postgresql(db, ts_migration_needed, posts_migration_needed, tp_hits_table_needed):
    """PostgreSQL-specific migration"""
    
    if ts_migration_needed:
        # Step 1: Add new columns to trading_signals
        logger.info("Adding new columns to trading_signals table...")

        await db.execute(text("""
            ALTER TABLE trading_signals
            ADD COLUMN IF NOT EXISTS take_profit_1 REAL,
            ADD COLUMN IF NOT EXISTS take_profit_2 REAL,
            ADD COLUMN IF NOT EXISTS take_profit_3 REAL,
            ADD COLUMN IF NOT EXISTS initial_position_size REAL DEFAULT 100.0,
            ADD COLUMN IF NOT EXISTS remaining_position_size REAL DEFAULT 100.0,
            ADD COLUMN IF NOT EXISTS break_even_triggered BOOLEAN DEFAULT FALSE;
        """))

        # Step 2: Migrate existing data
        logger.info("Migrating existing signal data...")

        await db.execute(text("""
            UPDATE trading_signals
            SET take_profit_1 = take_profit
            WHERE take_profit_1 IS NULL AND take_profit IS NOT NULL;
        """))

    if posts_migration_needed:
        # Step 3: Add new columns to posts table
        logger.info("Adding new columns to posts table...")

        await db.execute(text("""
            ALTER TABLE posts
            ADD COLUMN IF NOT EXISTS reply_to_message_id VARCHAR(50),
            ADD COLUMN IF NOT EXISTS tp_level INTEGER;
        """))

    if tp_hits_table_needed:
        # Step 4: Create take_profit_hits table
        logger.info("Creating take_profit_hits table...")

        await db.execute(text("""
            CREATE TABLE IF NOT EXISTS take_profit_hits (
                id SERIAL PRIMARY KEY,
                signal_id INTEGER NOT NULL REFERENCES trading_signals(id),
                tp_level INTEGER NOT NULL,
                tp_price REAL NOT NULL,
                hit_time TIMESTAMP WITH TIME ZONE NOT NULL,
                hit_price REAL NOT NULL,
                position_closed_percentage REAL NOT NULL,
                profit_loss_percentage REAL NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
            );
        """))

    await db.commit()

if __name__ == "__main__":
    import asyncio
    asyncio.run(migrate_multi_tp_support())
