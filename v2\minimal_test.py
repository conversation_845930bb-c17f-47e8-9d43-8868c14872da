#!/usr/bin/env python3
"""
Minimal test to check basic functionality
"""

import sys
import os
from pathlib import Path

# Add v2 to Python path
sys.path.insert(0, str(Path(__file__).parent))

print("Starting minimal test...")

try:
    # Test basic Python functionality
    print("✓ Python working")
    
    # Test environment
    from dotenv import load_dotenv
    load_dotenv()
    print("✓ Environment loaded")
    
    # Test core settings
    from core.config.settings import Settings
    settings = Settings()
    print("✓ Settings loaded")
    
    # Test database manager
    from database.connection.manager import DatabaseManager
    db_manager = DatabaseManager(settings)
    print("✓ Database manager created")
    
    print("\n🎉 Minimal test successful!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
