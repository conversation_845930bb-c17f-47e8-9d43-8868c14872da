#!/usr/bin/env python3
"""
MignalyBot v2 - Main Application Entry Point

This is the main entry point for MignalyBot v2, featuring:
- Performance optimized architecture
- AI-generated trading strategies
- Enhanced error handling and logging
- Improved async/await patterns
"""

import asyncio
import logging
import signal
import sys
from contextlib import asynccontextmanager
from typing import List

from core.config.settings import Settings
from core.logging.setup import setup_logging
from core.exceptions.base import MignalyBotException
from database.connection.manager import DatabaseManager
from services.application import ApplicationService


class MignalyBotV2:
    """Main application class for MignalyBot v2"""
    
    def __init__(self):
        self.settings = Settings()
        self.logger = None
        self.db_manager = None
        self.app_service = None
        self.running_tasks: List[asyncio.Task] = []
        self._shutdown_event = asyncio.Event()
    
    async def initialize(self):
        """Initialize all application components"""
        try:
            # Setup logging
            self.logger = setup_logging(self.settings)
            self.logger.info("Starting MignalyBot v2")
            
            # Initialize database manager
            self.db_manager = DatabaseManager(self.settings)
            await self.db_manager.initialize()
            
            # Initialize application service
            self.app_service = ApplicationService(self.settings, self.db_manager)
            await self.app_service.initialize()
            
            self.logger.info("MignalyBot v2 initialized successfully")
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Failed to initialize MignalyBot v2: {e}", exc_info=True)
            else:
                print(f"Failed to initialize MignalyBot v2: {e}")
            raise
    
    async def start(self):
        """Start all application services"""
        try:
            self.logger.info("Starting application services")
            
            # Start all services and collect their tasks
            service_tasks = await self.app_service.start_all_services()
            self.running_tasks.extend(service_tasks)
            
            # Setup signal handlers for graceful shutdown
            self._setup_signal_handlers()
            
            self.logger.info(f"MignalyBot v2 started with {len(self.running_tasks)} services")
            
            # Wait for shutdown signal
            await self._shutdown_event.wait()
            
        except Exception as e:
            self.logger.error(f"Error starting MignalyBot v2: {e}", exc_info=True)
            raise
    
    async def shutdown(self):
        """Gracefully shutdown all services"""
        try:
            self.logger.info("Shutting down MignalyBot v2")
            
            # Cancel all running tasks
            for task in self.running_tasks:
                if not task.done():
                    task.cancel()
            
            # Wait for tasks to complete with timeout
            if self.running_tasks:
                await asyncio.wait_for(
                    asyncio.gather(*self.running_tasks, return_exceptions=True),
                    timeout=30.0
                )
            
            # Shutdown application service
            if self.app_service:
                await self.app_service.shutdown()
            
            # Close database connections
            if self.db_manager:
                await self.db_manager.close()
            
            self.logger.info("MignalyBot v2 shutdown completed")
            
        except asyncio.TimeoutError:
            self.logger.warning("Shutdown timeout reached, forcing exit")
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}", exc_info=True)
    
    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            self.logger.info(f"Received signal {signum}, initiating shutdown")
            self._shutdown_event.set()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    @asynccontextmanager
    async def lifespan(self):
        """Context manager for application lifespan"""
        try:
            await self.initialize()
            yield self
        finally:
            await self.shutdown()


async def main():
    """Main application function"""
    app = MignalyBotV2()
    
    try:
        async with app.lifespan():
            await app.start()
    except KeyboardInterrupt:
        print("\nApplication stopped by user")
    except MignalyBotException as e:
        print(f"MignalyBot error: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # Set event loop policy for Windows compatibility
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        pass
