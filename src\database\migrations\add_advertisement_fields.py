#!/usr/bin/env python3
"""
Migration to add advertisement fields to channels table
"""

import sqlite3
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

def migrate_advertisement_fields():
    """Add advertisement fields to channels table"""
    
    # Database path
    db_path = Path("data/mignalybot.db")
    
    if not db_path.exists():
        logger.warning("Database file not found, skipping migration")
        return
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # Check if columns already exist
        cursor.execute("PRAGMA table_info(channels)")
        columns = [column[1] for column in cursor.fetchall()]
        
        migrations_needed = []
        
        if 'enable_advertisement' not in columns:
            migrations_needed.append("ALTER TABLE channels ADD COLUMN enable_advertisement BOOLEAN DEFAULT 0")
            
        if 'advertisement_text' not in columns:
            migrations_needed.append("ALTER TABLE channels ADD COLUMN advertisement_text VARCHAR(500) DEFAULT 'This message generated by Mignaly'")
            
        if 'advertisement_url' not in columns:
            migrations_needed.append("ALTER TABLE channels ADD COLUMN advertisement_url VARCHAR(255) DEFAULT 'https://mignaly.com'")
        
        if migrations_needed:
            logger.info(f"Running {len(migrations_needed)} advertisement field migrations...")
            
            for migration in migrations_needed:
                logger.info(f"Executing: {migration}")
                cursor.execute(migration)
            
            conn.commit()
            logger.info("✅ Advertisement fields migration completed successfully")
        else:
            logger.info("✅ Advertisement fields already exist, no migration needed")
            
    except Exception as e:
        logger.error(f"❌ Error during advertisement fields migration: {e}")
        if conn:
            conn.rollback()
        raise
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    migrate_advertisement_fields()
