#!/usr/bin/env python3
"""
Database migration to fix timezone consistency across the codebase
This migration ensures all datetime fields are properly timezone-aware
"""

import asyncio
import logging
import os
import sys
from datetime import datetime
import pytz

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.database.setup import init_db, get_async_db, is_sqlite_db
from src.database.models import (
    Channel, NewsItem, EconomicEvent, TradingSignal, Post, Config
)
from sqlalchemy import text

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def migrate_timezone_consistency():
    """
    Migrate database to ensure timezone consistency
    """
    logger.info("Starting timezone consistency migration...")
    
    try:
        # Initialize database
        await init_db()
        
        async for db in get_async_db():
            try:
                # For SQLite, we don't need to modify column types as they're flexible
                # For PostgreSQL, we would need to alter column types to TIMESTAMPTZ
                
                if not is_sqlite_db():
                    logger.info("Updating PostgreSQL datetime columns to use timezone...")
                    
                    # Update datetime columns to use timezone
                    await db.execute(text("""
                        ALTER TABLE channels 
                        ALTER COLUMN created_at TYPE TIMESTAMPTZ USING created_at AT TIME ZONE 'UTC',
                        ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING updated_at AT TIME ZONE 'UTC'
                    """))
                    
                    await db.execute(text("""
                        ALTER TABLE news_items 
                        ALTER COLUMN published_at TYPE TIMESTAMPTZ USING published_at AT TIME ZONE 'UTC',
                        ALTER COLUMN created_at TYPE TIMESTAMPTZ USING created_at AT TIME ZONE 'UTC'
                    """))
                    
                    await db.execute(text("""
                        ALTER TABLE economic_events 
                        ALTER COLUMN event_time TYPE TIMESTAMPTZ USING event_time AT TIME ZONE 'UTC',
                        ALTER COLUMN created_at TYPE TIMESTAMPTZ USING created_at AT TIME ZONE 'UTC'
                    """))
                    
                    await db.execute(text("""
                        ALTER TABLE trading_signals 
                        ALTER COLUMN entry_time TYPE TIMESTAMPTZ USING entry_time AT TIME ZONE 'UTC',
                        ALTER COLUMN exit_time TYPE TIMESTAMPTZ USING exit_time AT TIME ZONE 'UTC',
                        ALTER COLUMN created_at TYPE TIMESTAMPTZ USING created_at AT TIME ZONE 'UTC',
                        ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING updated_at AT TIME ZONE 'UTC'
                    """))
                    
                    await db.execute(text("""
                        ALTER TABLE posts 
                        ALTER COLUMN created_at TYPE TIMESTAMPTZ USING created_at AT TIME ZONE 'UTC',
                        ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING updated_at AT TIME ZONE 'UTC',
                        ALTER COLUMN scheduled_time TYPE TIMESTAMPTZ USING scheduled_time AT TIME ZONE 'UTC',
                        ALTER COLUMN sent_at TYPE TIMESTAMPTZ USING sent_at AT TIME ZONE 'UTC'
                    """))
                    
                    await db.commit()
                    logger.info("PostgreSQL timezone migration completed")
                
                else:
                    logger.info("SQLite detected - no column type changes needed")
                
                # Update any existing naive datetime records to be timezone-aware
                await update_existing_records(db)
                
                logger.info("✅ Timezone consistency migration completed successfully!")
                break
                
            except Exception as e:
                logger.error(f"Error during migration: {e}", exc_info=True)
                if not is_sqlite_db():
                    await db.rollback()
                break
                
    except Exception as e:
        logger.error(f"Migration failed: {e}", exc_info=True)

async def update_existing_records(db):
    """
    Update existing records to ensure they have proper timezone information
    """
    logger.info("Updating existing records for timezone consistency...")
    
    try:
        # For SQLite, we'll assume all existing naive datetimes are in UTC
        # For PostgreSQL, the ALTER TABLE commands above handle this
        
        if is_sqlite_db():
            # SQLite stores datetimes as strings, so we don't need to update them
            # The application code will handle timezone conversion
            logger.info("SQLite records will be handled by application timezone conversion")
        
        logger.info("Existing records updated successfully")
        
    except Exception as e:
        logger.error(f"Error updating existing records: {e}")

if __name__ == "__main__":
    asyncio.run(migrate_timezone_consistency())
