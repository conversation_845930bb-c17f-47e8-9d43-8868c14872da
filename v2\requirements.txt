# Core dependencies for MignalyBot v2
python-telegram-bot==20.6
fastapi==0.104.1
uvicorn[standard]==0.23.2
sqlalchemy==2.0.23
alembic==1.12.1
pydantic==2.4.2
python-dotenv==1.0.0
httpx==0.25.1
apscheduler==3.10.4
jinja2==3.1.2

# Async database drivers
asyncpg==0.29.0  # PostgreSQL async driver
aiomysql==0.2.0  # MySQL async driver

# Data collection and processing
yfinance==0.2.31
alpha_vantage==2.3.1
pandas==2.0.3
pandas-ta==0.3.14b0
numpy==1.24.3
beautifulsoup4==4.12.2
feedparser==6.0.10

# Visualization and charts
matplotlib==3.7.2
plotly==5.18.0
kaleido==0.2.1
Pillow==10.1.0

# AI integration
openai==1.3.0  # For Qwen API integration

# Web admin interface
aiofiles==23.2.1
python-multipart==0.0.6

# Performance monitoring
psutil==5.9.6
memory-profiler==0.61.0

# Timezone handling
pytz==2023.3

# Caching
redis==5.0.1
aioredis==2.0.1

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.1  # For testing HTTP clients

# Development tools
black==23.9.1
isort==5.12.0
flake8==6.1.0
mypy==1.6.1

# Security
cryptography==41.0.7
bcrypt==4.0.1

# Logging
structlog==23.2.0

# Configuration validation
pydantic-settings==2.0.3

# Rate limiting
slowapi==0.1.9

# Background tasks
celery==5.3.4
redis==5.0.1  # For Celery broker

# Monitoring and metrics
prometheus-client==0.19.0

# Data validation
cerberus==1.3.5

# Utilities
click==8.1.7
rich==13.6.0
tqdm==4.66.1
