#!/usr/bin/env python3
"""
Migration script to fix channel attributes
Ensures all channels have required fields with proper defaults
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.database.setup import get_async_db, is_sqlite_db
from src.database.models import Channel
from sqlalchemy import select, update, text

logger = logging.getLogger(__name__)

async def fix_channel_attributes():
    """
    Fix channel attributes to ensure all channels have required fields
    """
    logger.info("🔧 Starting channel attributes fix...")
    
    try:
        async for db in get_async_db():
            # Get all channels
            if is_sqlite_db():
                channels_result = db.execute(select(Channel))
            else:
                channels_result = await db.execute(select(Channel))
            
            channels = channels_result.scalars().all()
            
            if not channels:
                logger.info("No channels found in database")
                return
            
            logger.info(f"Found {len(channels)} channels to check")
            
            updated_count = 0
            
            for channel in channels:
                needs_update = False
                updates = {}
                
                # Check language field
                if not hasattr(channel, 'language') or not channel.language:
                    updates['language'] = 'en'
                    needs_update = True
                    logger.info(f"Channel {channel.name}: Setting language to 'en'")
                
                # Check brand_name field
                if not hasattr(channel, 'brand_name') or not channel.brand_name:
                    updates['brand_name'] = channel.name
                    needs_update = True
                    logger.info(f"Channel {channel.name}: Setting brand_name to '{channel.name}'")
                
                # Check post_types field
                if not hasattr(channel, 'post_types') or not channel.post_types:
                    updates['post_types'] = 'news,signal,analysis,event,performance,greeting'
                    needs_update = True
                    logger.info(f"Channel {channel.name}: Setting default post_types")
                
                # Update channel if needed
                if needs_update:
                    if is_sqlite_db():
                        db.execute(
                            update(Channel)
                            .where(Channel.id == channel.id)
                            .values(**updates)
                        )
                    else:
                        await db.execute(
                            update(Channel)
                            .where(Channel.id == channel.id)
                            .values(**updates)
                        )
                    
                    updated_count += 1
                    logger.info(f"✅ Updated channel: {channel.name}")
            
            # Commit changes
            if is_sqlite_db():
                db.commit()
            else:
                await db.commit()
            
            logger.info(f"🎉 Channel attributes fix completed! Updated {updated_count} channels")
            
    except Exception as e:
        logger.error(f"❌ Error fixing channel attributes: {e}", exc_info=True)
        if not is_sqlite_db():
            await db.rollback()
        else:
            db.rollback()
        raise

async def verify_channel_attributes():
    """
    Verify that all channels have the required attributes
    """
    logger.info("🔍 Verifying channel attributes...")
    
    try:
        async for db in get_async_db():
            # Get all channels
            if is_sqlite_db():
                channels_result = db.execute(select(Channel))
            else:
                channels_result = await db.execute(select(Channel))
            
            channels = channels_result.scalars().all()
            
            all_good = True
            
            for channel in channels:
                issues = []
                
                if not hasattr(channel, 'language') or not channel.language:
                    issues.append("missing language")
                
                if not hasattr(channel, 'brand_name') or not channel.brand_name:
                    issues.append("missing brand_name")
                
                if not hasattr(channel, 'post_types') or not channel.post_types:
                    issues.append("missing post_types")
                
                if issues:
                    logger.warning(f"Channel {channel.name} has issues: {', '.join(issues)}")
                    all_good = False
                else:
                    logger.info(f"✅ Channel {channel.name}: language={channel.language}, brand_name={channel.brand_name}")
            
            if all_good:
                logger.info("🎉 All channels have required attributes!")
            else:
                logger.warning("⚠️ Some channels still have missing attributes")
                
    except Exception as e:
        logger.error(f"❌ Error verifying channel attributes: {e}", exc_info=True)
        raise

async def main():
    """
    Main migration function
    """
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger.info("🚀 Starting channel attributes migration...")
    
    try:
        # First verify current state
        await verify_channel_attributes()
        
        # Fix any issues
        await fix_channel_attributes()
        
        # Verify again
        await verify_channel_attributes()
        
        logger.info("✅ Migration completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Migration failed: {e}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
