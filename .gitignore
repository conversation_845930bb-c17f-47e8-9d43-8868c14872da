# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/
.pytest_cache/
cover/
*.mo
*.pot
*.log
local_settings.py
instance/
.webassets-cache
.pybuilder/
target/

# Virtual Environment
venv/
env/
ENV/
.env
.venv
env.bak/
venv.bak/
pythonenv*

# IDE
.idea/
.vscode/
*.swp
*.swo
*~
.spyderproject
.spyproject
.ropeproject
.project
.pydevproject
.settings/

# Project specific
.env
*.db
*.sqlite
*.sqlite3
mignalybot.db
logs/
images/
data/
*.log.*
*.chart.png
*.signal.png
*.news.png
*.event.png
*.performance.png
temp/

# Docker
.dockerignore
docker-compose.override.yml
docker-compose.*.yml
.docker/
docker-data/

# Testing
.coverage
coverage.xml
*.cover
.hypothesis/
.pytest_cache/
nosetests.xml
coverage/

# OS specific
.DS_Store
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.lnk
._*
.Spotlight-V100
.Trashes
Icon?

# Test and debug files (production cleanup)
test_*.py
debug_*.py
check_*.py
create_test_*.py
create_sample_*.py
verify_*.py
demo_*.py
diagnose_*.py
fix_*.py
generate_*test*.py
generate_*sample*.py
validate_*.py
comprehensive_*.py
collect_test_*.py

# Development artifacts
*_test.py
*_debug.py
*_sample.py
*_temp.py
sample_*.png
test_*.png
test_*.json
*.tmp
*.temp
*.bak
*.backup
*.zip
deployment.log

# Test and debug files (production cleanup)
test_*.py
debug_*.py
check_*.py
create_test_*.py
create_sample_*.py
verify_*.py
demo_*.py
diagnose_*.py
fix_*.py
generate_*test*.py
generate_*sample*.py
validate_*.py
comprehensive_*.py
collect_test_*.py

# Development artifacts
*_test.py
*_debug.py
*_sample.py
*_temp.py
sample_*.png
test_*.png
test_*.json
*.tmp
*.temp
*.bak
*.backup
*.zip
deployment.log

# Documentation (keep only essential)
*_SUMMARY.md
*_IMPLEMENTATION.md
*_UPDATE.md
*_FIX.md
*_INTEGRATION.md
CHANGELOG.md
MIGRATION_*.md
SIMPLE_*.md
DOCKER_*.md
*_ENHANCEMENT.md

# Migration and setup scripts (development only)
migrate_*.py
reset_*.py
add_*.py
update_*.py
run_*.py
setup_*.py
cleanup_*.py
remove_*.py
