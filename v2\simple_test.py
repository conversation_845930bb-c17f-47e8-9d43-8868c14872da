#!/usr/bin/env python3
"""
Simple test to check basic functionality
"""

print("Starting simple test...")

try:
    # Test basic imports
    import os
    import sys
    import asyncio
    print("✓ Basic imports successful")
    
    # Test environment loading
    from dotenv import load_dotenv
    load_dotenv()
    print("✓ Environment loading successful")
    
    # Test database imports
    import sqlalchemy
    print("✓ SQLAlchemy import successful")
    
    # Test Telegram imports
    import telegram
    print("✓ Telegram import successful")
    
    # Test FastAPI imports
    import fastapi
    print("✓ FastAPI import successful")
    
    print("\n🎉 Basic test completed successfully!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
