#!/usr/bin/env python3
"""
Simple test script to verify the fixes are in place
Tests the code changes without requiring full application setup
"""

import os
import sys
import re
from pathlib import Path

def test_sticker_broadcasting_fix():
    """Test that sticker broadcasting fix is in place"""
    print("🔍 Testing sticker broadcasting fix...")
    
    bot_file = Path("src/telegram/bot.py")
    if not bot_file.exists():
        print("❌ FAIL: src/telegram/bot.py not found")
        return False
    
    content = bot_file.read_text(encoding='utf-8')
    
    # Check if the fix for including PUBLISHED status in cleanup logic is present
    if "PostStatus.PUBLISHED" in content and "PostStatus.SCHEDULED, PostStatus.PROCESSING, PostStatus.PUBLISHED" in content:
        print("✅ PASS: Sticker cleanup logic includes PUBLISHED status")
        success = True
    else:
        print("❌ FAIL: Sticker cleanup logic not updated")
        success = False
    
    # Check if orphaned file cleanup has age check
    if "file_age.total_seconds() > 3600" in content:
        print("✅ PASS: Orphaned file cleanup has age check (1 hour)")
        success = success and True
    else:
        print("❌ FAIL: Orphaned file cleanup missing age check")
        success = False
    
    return success

def test_economic_calendar_fix():
    """Test that economic calendar API fix is in place"""
    print("\n🔍 Testing economic calendar API fix...")
    
    calendar_file = Path("src/data_collection/economic_calendar.py")
    if not calendar_file.exists():
        print("❌ FAIL: src/data_collection/economic_calendar.py not found")
        return False
    
    content = calendar_file.read_text(encoding='utf-8')
    
    # Check if JBlanked API endpoints are added
    if "jblanked.com" in content:
        print("✅ PASS: JBlanked API endpoints added")
        success = True
    else:
        print("❌ FAIL: JBlanked API endpoints not found")
        success = False
    
    # Check if parsing functions exist
    if "parse_jblanked_events" in content and "parse_forex_factory_events" in content:
        print("✅ PASS: Parsing functions for both API formats exist")
        success = success and True
    else:
        print("❌ FAIL: Parsing functions missing")
        success = False
    
    # Check if better error handling for HTTP status codes is present
    if "response.status_code == 404" in content and "response.status_code == 500" in content:
        print("✅ PASS: Improved HTTP status code handling")
        success = success and True
    else:
        print("❌ FAIL: HTTP status code handling not improved")
        success = False
    
    return success

def test_market_data_fix():
    """Test that market data API fix is in place"""
    print("\n🔍 Testing market data API fix...")
    
    market_file = Path("src/data_collection/market_data.py")
    if not market_file.exists():
        print("❌ FAIL: src/data_collection/market_data.py not found")
        return False
    
    content = market_file.read_text(encoding='utf-8')
    
    # Check if retry logic for initial request is added
    if "max_request_retries" in content:
        print("✅ PASS: Retry logic for initial request added")
        success = True
    else:
        print("❌ FAIL: Retry logic for initial request missing")
        success = False
    
    # Check if improved error handling for 500 errors is present
    if "MT5Connector service issue" in content:
        print("✅ PASS: Improved MT5Connector error handling")
        success = success and True
    else:
        print("❌ FAIL: MT5Connector error handling not improved")
        success = False
    
    # Check if progressive wait times are implemented
    if "wait_time = min(5 + (attempt * 2), 15)" in content:
        print("✅ PASS: Progressive wait times implemented")
        success = success and True
    else:
        print("❌ FAIL: Progressive wait times not implemented")
        success = False
    
    return success

def test_telegram_connection_fix():
    """Test that Telegram connection fix is in place"""
    print("\n🔍 Testing Telegram connection improvements...")
    
    bot_file = Path("src/telegram/bot.py")
    if not bot_file.exists():
        print("❌ FAIL: src/telegram/bot.py not found")
        return False
    
    content = bot_file.read_text(encoding='utf-8')
    
    # Check if HTTPXRequest with custom settings is used
    if "HTTPXRequest" in content and "connection_pool_size" in content:
        print("✅ PASS: Custom HTTPXRequest with connection settings")
        success = True
    else:
        print("❌ FAIL: Custom HTTPXRequest not implemented")
        success = False
    
    # Check if network connectivity test function exists
    if "test_network_connectivity" in content:
        print("✅ PASS: Network connectivity test function exists")
        success = success and True
    else:
        print("❌ FAIL: Network connectivity test function missing")
        success = False
    
    # Check if health monitoring is added
    if "health_check_interval" in content:
        print("✅ PASS: Health monitoring implemented")
        success = success and True
    else:
        print("❌ FAIL: Health monitoring not implemented")
        success = False
    
    # Check if error callback for polling is added
    if "polling_error_callback" in content:
        print("✅ PASS: Polling error callback implemented")
        success = success and True
    else:
        print("❌ FAIL: Polling error callback missing")
        success = False
    
    return success

def test_api_endpoints():
    """Test that API endpoints are accessible"""
    print("\n🔍 Testing API endpoint accessibility...")
    
    try:
        import httpx
        import asyncio
        
        async def test_endpoints():
            results = []
            
            # Test Forex Factory API
            try:
                async with httpx.AsyncClient(timeout=10.0) as client:
                    response = await client.get("https://nfs.faireconomy.media/ff_calendar_thisweek.json")
                    if response.status_code == 200:
                        print("✅ PASS: Forex Factory API accessible")
                        results.append(True)
                    else:
                        print(f"⚠️ WARN: Forex Factory API returned {response.status_code}")
                        results.append(False)
            except Exception as e:
                print(f"❌ FAIL: Forex Factory API error: {e}")
                results.append(False)
            
            # Test MT5 API
            try:
                async with httpx.AsyncClient(timeout=10.0) as client:
                    response = await client.get("http://154.53.166.53:8000/api/market/symbols")
                    if response.status_code == 200:
                        print("✅ PASS: MT5 API accessible")
                        results.append(True)
                    else:
                        print(f"⚠️ WARN: MT5 API returned {response.status_code}")
                        results.append(False)
            except Exception as e:
                print(f"❌ FAIL: MT5 API error: {e}")
                results.append(False)
            
            # Test Telegram API
            try:
                async with httpx.AsyncClient(timeout=10.0) as client:
                    response = await client.get("https://api.telegram.org")
                    if response.status_code < 500:  # Any response except server error
                        print("✅ PASS: Telegram API accessible")
                        results.append(True)
                    else:
                        print(f"⚠️ WARN: Telegram API returned {response.status_code}")
                        results.append(False)
            except Exception as e:
                print(f"❌ FAIL: Telegram API error: {e}")
                results.append(False)
            
            return all(results)
        
        return asyncio.run(test_endpoints())
        
    except ImportError:
        print("❌ FAIL: httpx not available for API testing")
        return False
    except Exception as e:
        print(f"❌ FAIL: API testing error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Running simple fix verification tests")
    print("=" * 50)
    
    tests = [
        ("Sticker Broadcasting Fix", test_sticker_broadcasting_fix),
        ("Economic Calendar API Fix", test_economic_calendar_fix),
        ("Market Data API Fix", test_market_data_fix),
        ("Telegram Connection Fix", test_telegram_connection_fix),
        ("API Endpoint Accessibility", test_api_endpoints),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ FAIL: {test_name} - Error: {e}")
            results.append(False)
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    print(f"Total tests: {total}")
    print(f"Passed: {passed}")
    print(f"Failed: {total - passed}")
    print(f"Success rate: {(passed/total*100):.1f}%")
    
    if passed == total:
        print("\n🎉 All fixes verified successfully!")
        return 0
    else:
        print(f"\n⚠️ {total - passed} test(s) failed. Please review the fixes.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
