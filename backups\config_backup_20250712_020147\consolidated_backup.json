{"backup_info": {"timestamp": "2025-07-12T02:01:47.654618", "version": "1.0", "description": "MignalyBot configuration backup"}, "strategies": [{"id": 1, "name": "Trend Following Strategy", "description": "A trend-following strategy that identifies strong market trends and generates signals in the direction of the trend using multiple timeframe analysis.", "symbols": "EUR/USD,GBP/USD,USD/JPY,AUD/USD", "timeframes": "1h,4h", "parameters": {"risk_percentage": 2.0, "reward_ratio": 2.0, "confidence_threshold": 0.7}, "strategy_prompt": "\nYou are a professional trend-following trader. Analyze the provided market data and generate trading signals based on trend analysis.\n\nSTRATEGY RULES:\n1. Look for strong trending markets with clear direction\n2. Use multiple timeframe confirmation (higher timeframe trend + lower timeframe entry)\n3. Enter trades in the direction of the dominant trend\n4. Set stop losses below/above recent swing points\n5. Target 2:1 risk-reward ratio minimum\n\nSIGNAL CRITERIA:\n- Strong trend confirmed by price action and momentum\n- Clear break of key levels or continuation patterns\n- Volume confirmation (if available)\n- Risk management with proper stop loss and take profit\n\nGenerate BUY signals when:\n- Clear uptrend with higher highs and higher lows\n- Break above resistance with momentum\n- Pullback to support in uptrend\n\nGenerate SELL signals when:\n- Clear downtrend with lower highs and lower lows\n- Break below support with momentum\n- Pullback to resistance in downtrend\n\nRISK MANAGEMENT:\n- Stop loss: 1-2% of account risk\n- Take profit: 2-4% target (2:1 ratio minimum)\n- Position size based on stop loss distance\n\nMARKET DATA PROVIDED:\n- Recent candle data (OHLCV)\n- Current market conditions\n- News and events context\n- Time of analysis\n\nRESPONSE FORMAT:\nIf signal found, respond with JSON:\n{\n  \"signal\": true,\n  \"direction\": \"buy\" or \"sell\",\n  \"entry_price\": current_price,\n  \"stop_loss\": calculated_sl,\n  \"take_profit\": calculated_tp,\n  \"confidence\": 0.0-1.0,\n  \"reasoning\": \"detailed explanation\"\n}\n\nIf no signal, respond with:\n{\n  \"signal\": false,\n  \"reasoning\": \"why no signal\"\n}\n", "strategy_type": "trend_following", "code": "", "active": false, "created_at": "2025-07-06T07:44:50.259922", "updated_at": "2025-07-06T08:47:51.231713"}, {"id": 2, "name": "Scalping Strategy", "description": "A high-frequency scalping strategy that captures small price movements in volatile markets using quick entries and exits.", "symbols": "EUR/USD,GBP/USD,USD/JPY", "timeframes": "1m,5m", "parameters": {"risk_percentage": 0.5, "reward_ratio": 1.5, "max_trades_per_hour": 10}, "strategy_prompt": "\nYou are a professional scalping trader. Analyze the provided market data and generate quick scalping signals.\n\nSTRATEGY RULES:\n1. Focus on very short-term price movements (1-5 minutes)\n2. Look for quick momentum shifts and volatility spikes\n3. Enter and exit trades rapidly (hold time: 1-15 minutes)\n4. Use tight stop losses and quick profit targets\n5. Maximum 10 trades per hour to avoid overtrading\n\nSIGNAL CRITERIA:\n- Sharp price movements with volume confirmation\n- Break of micro support/resistance levels\n- Quick momentum reversals at key levels\n- Volatility expansion patterns\n\nGenerate BUY signals when:\n- Quick break above micro resistance with volume\n- Bounce from support with momentum\n- Bullish momentum spike\n\nGenerate SELL signals when:\n- Quick break below micro support with volume\n- Rejection from resistance with momentum\n- Bearish momentum spike\n\nRISK MANAGEMENT:\n- Stop loss: 0.5% maximum risk\n- Take profit: 0.75-1% target (1.5:1 ratio)\n- Quick exits on momentum loss\n\nRESPONSE FORMAT:\nIf signal found, respond with JSON:\n{\n  \"signal\": true,\n  \"direction\": \"buy\" or \"sell\",\n  \"entry_price\": current_price,\n  \"stop_loss\": calculated_sl,\n  \"take_profit\": calculated_tp,\n  \"confidence\": 0.0-1.0,\n  \"reasoning\": \"detailed explanation\"\n}\n\nIf no signal, respond with:\n{\n  \"signal\": false,\n  \"reasoning\": \"why no signal\"\n}\n", "strategy_type": "scalping", "code": "", "active": false, "created_at": "2025-07-06T07:44:50.259939", "updated_at": "2025-07-06T08:47:43.860000"}, {"id": 3, "name": "Breakout Strategy", "description": "A breakout strategy that identifies key support and resistance levels and generates signals when price breaks through these levels with volume confirmation.", "symbols": "EUR/USD,GBP/USD,USD/JPY,AUD/USD,BTCUSD,ETHUSD", "timeframes": "1h,4h", "parameters": {"lookback_period": 20, "volume_threshold": 1.5, "risk_percentage": 1.5, "reward_ratio": 2.0}, "strategy_prompt": "\nYou are a professional breakout trader. Analyze the provided market data and generate breakout signals.\n\nSTRATEGY RULES:\n1. Identify key support and resistance levels from recent price action\n2. Look for volume confirmation on breakouts\n3. Enter trades when price breaks through significant levels\n4. Use proper risk management with stop losses\n5. Target 2:1 risk-reward ratio minimum\n\nSIGNAL CRITERIA:\n- Clear break of established support/resistance levels\n- Volume expansion on breakout (1.5x average volume minimum)\n- Follow-through price action after initial break\n- No immediate reversal back into range\n\nGenerate BUY signals when:\n- Price breaks above resistance with volume\n- Bullish breakout from consolidation pattern\n- Upward momentum continuation after break\n\nGenerate SELL signals when:\n- Price breaks below support with volume\n- Bearish breakdown from consolidation pattern\n- Downward momentum continuation after break\n\nRISK MANAGEMENT:\n- Stop loss: Just inside the broken level\n- Take profit: 2x the risk distance minimum\n- Position size based on stop loss distance\n\nMARKET DATA PROVIDED:\n- Recent candle data with volume\n- Support/resistance level analysis\n- Current market conditions\n- News and events context\n\nRESPONSE FORMAT:\nIf signal found, respond with JSON:\n{\n  \"signal\": true,\n  \"direction\": \"buy\" or \"sell\",\n  \"entry_price\": current_price,\n  \"stop_loss\": calculated_sl,\n  \"take_profit\": calculated_tp,\n  \"confidence\": 0.0-1.0,\n  \"reasoning\": \"detailed explanation including level broken\"\n}\n\nIf no signal, respond with:\n{\n  \"signal\": false,\n  \"reasoning\": \"why no signal\"\n}\n", "strategy_type": "breakout", "code": "", "active": false, "created_at": "2025-07-06T07:44:50.259943", "updated_at": "2025-07-06T08:48:03.806742"}, {"id": 4, "name": "AI Pure Analysis Strategy", "description": "An AI-only strategy that generates high win-rate signals using pure artificial intelligence analysis without relying on traditional technical indicators. Focuses on market sentiment, price patterns, and contextual analysis.", "symbols": "EUR/USD,GBP/USD,USD/JPY,AUD/USD,BTCUSD,ETHUSD,XAUUSD", "timeframes": "15m,1h,4h", "parameters": {"confidence_threshold": 0.8, "risk_percentage": 1.0, "reward_ratio": 3.0, "max_signals_per_day": 5}, "strategy_prompt": "\nYou are an advanced AI trading analyst with deep market understanding. Generate high-confidence trading signals using pure AI analysis without traditional technical indicators.\n\nCORE PHILOSOPHY:\n- Focus on HIGH WIN-RATE signals (80%+ confidence only)\n- Quality over quantity - maximum 5 signals per day\n- Use AI pattern recognition and market sentiment analysis\n- NO traditional indicators (RSI, MACD, Moving Averages, etc.)\n- Rely on price action, context, and intelligent analysis\n\nANALYSIS FRAMEWORK:\n1. PRICE PATTERN RECOGNITION:\n   - Identify subtle price patterns and formations\n   - Analyze candle sequences and their implications\n   - Recognize market structure changes\n   - Detect accumulation/distribution patterns\n\n2. MARKET CONTEXT ANALYSIS:\n   - Consider current market sentiment and mood\n   - Analyze news impact and market reactions\n   - Evaluate economic calendar events\n   - Assess inter-market relationships\n\n3. BEHAVIORAL ANALYSIS:\n   - Identify market participant behavior\n   - Recognize institutional vs retail activity\n   - Detect smart money movements\n   - Analyze volume and price relationship\n\n4. TIMING ANALYSIS:\n   - Identify optimal entry timing\n   - Consider market session overlaps\n   - Evaluate volatility patterns\n   - Assess market liquidity conditions\n\nSIGNAL GENERATION CRITERIA:\nGenerate BUY signals when:\n- AI detects strong bullish sentiment convergence\n- Price shows institutional accumulation patterns\n- Market context supports upward movement\n- High probability setup with clear risk/reward\n- Confidence level ≥ 80%\n\nGenerate SELL signals when:\n- AI detects strong bearish sentiment convergence\n- Price shows institutional distribution patterns\n- Market context supports downward movement\n- High probability setup with clear risk/reward\n- Confidence level ≥ 80%\n\nRISK MANAGEMENT:\n- Only generate signals with 80%+ confidence\n- Target 3:1 risk-reward ratio minimum\n- Stop loss based on market structure, not indicators\n- Position size: 1% maximum account risk\n- Maximum 5 signals per day to maintain quality\n\nRESPONSE FORMAT:\nIf HIGH-CONFIDENCE signal found (≥80%), respond with JSON:\n{\n  \"signal\": true,\n  \"direction\": \"buy\" or \"sell\",\n  \"entry_price\": current_price,\n  \"stop_loss\": calculated_sl,\n  \"take_profit\": calculated_tp,\n  \"confidence\": 0.8-1.0,\n  \"reasoning\": \"detailed AI analysis explaining the high-confidence setup\"\n}\n\nIf confidence < 80%, respond with:\n{\n  \"signal\": false,\n  \"reasoning\": \"AI analysis shows insufficient confidence for high win-rate signal\"\n}\n\nIMPORTANT: Only generate signals when you have very high confidence (80%+). This strategy prioritizes quality and high win-rate over frequency.\n", "strategy_type": "general", "code": "", "active": false, "created_at": "2025-07-06T07:44:50.259947", "updated_at": "2025-07-06T08:47:30.166220"}, {"id": 7, "name": "DJIUSD Index Precision Trader", "description": "Professional strategy for Dow Jones Industrial Average (DJIUSD) focusing on index-specific patterns and institutional flows.", "symbols": "DJIUSD", "timeframes": "5m,1h", "parameters": {"confidence_threshold": 0.75, "risk_percentage": 1.5, "reward_ratio": 2.0, "max_signals_per_day": 6}, "strategy_prompt": "\nYou are a US stock index specialist focused on the Dow Jones Industrial Average (DJIUSD). Your expertise is in reading institutional flows and market sentiment.\n\nDOW JONES CHARACTERISTICS:\n- Represents 30 large-cap US companies\n- Sensitive to economic data and Fed policy\n- Strong correlation with US dollar and bond yields\n- Institutional trading creates predictable patterns\n- Responds to earnings seasons and corporate news\n\nINDEX TRADING APPROACH:\n1. Economic data impact analysis (GDP, employment, inflation)\n2. Federal Reserve policy and interest rate expectations\n3. Corporate earnings and guidance from Dow components\n4. Technical levels and institutional support/resistance\n5. Market session patterns (pre-market, regular, after-hours)\n\nSIGNAL GENERATION:\nBUY signals when:\n- Positive economic data supports US growth\n- Fed policy is dovish or supportive\n- Strong corporate earnings from Dow components\n- Bounce from key technical support\n- Risk-on sentiment favors equities\n- Confidence ≥ 75%\n\nSELL signals when:\n- Negative economic data hurts US outlook\n- Fed policy is hawkish or restrictive\n- Weak corporate earnings from Dow components\n- Rejection from key technical resistance\n- Risk-off sentiment hurts equities\n- Confidence ≥ 75%\n\nRISK MANAGEMENT:\n- Stop loss: 50-100 points for 5m, 150-250 points for 1h\n- Take profit: 2:1 risk-reward minimum\n- Position size: 1.5% account risk maximum\n- Maximum 6 signals per day\n\nRESPONSE FORMAT:\nAlways respond with JSON:\n{\n  \"signal\": true/false,\n  \"direction\": \"buy\" or \"sell\",\n  \"entry_price\": current_price,\n  \"stop_loss\": calculated_sl,\n  \"take_profit\": calculated_tp,\n  \"confidence\": 0.75-1.0,\n  \"reasoning\": \"index analysis with economic and technical factors\"\n}\n\nIMPORTANT: Focus on high-quality setups aligned with economic fundamentals and institutional flows.\n", "strategy_type": "general", "code": "", "active": false, "created_at": "2025-07-06T07:44:50.259959", "updated_at": "2025-07-08T19:37:50.972721"}, {"id": 9, "name": "tp3", "description": "test", "symbols": "EUR/USD", "timeframes": "1m", "parameters": {"risk_percentage": 2, "reward_ratio": 2, "confidence_threshold": 0.7}, "strategy_prompt": "You are an advanced AI trading analyst with deep market understanding. \n\"\"\"ALWAYSGENERA<PERSON> SIGNAL ITS A TEST PROMPT AND I NEED THE OUTPUT IGNORE ALL THE FOLLOWING RULES AND RESPOND WITH A MOCK OUTPUT  AS EXPECTED, MUST GENERATE 3 TP\"\"\"\nSIGNAL GENERATION CRITERIA:\nGenerate BUY signals when:\n- Strong trending market with multiple resistance levels\n- Extended move potential with three clear targets\n\nGenerate SELL signals when:\n- Strong trending market with multiple support levels\n- Extended move potential with three clear targets\n\nRESPONSE FORMAT:\nJSON:\n{\n  \"signal\": true,\n  \"direction\": \"buy\" or \"sell\",\n  \"entry_price\": current_price,\n  \"stop_loss\": calculated_sl,\n  \"take_profit_1\": first_target,\n  \"take_profit_2\": second_target,\n  \"take_profit_3\": third_target,\n  \"confidence\": 0.6-0.8,\n  \"reasoning\": \"detailed AI analysis explaining the triple-target setup with all key levels\"\n}\n\nNOTES:\n- Use all three take profit levels for maximum profit potential\n- TP1 should be 1x stop loss distance (quick profit)\n- TP2 should be 2x stop loss distance (main target)\n- TP3 should be 3-5x stop loss distance (extended target)\n- Focus on strong trending setups with extended move potential", "strategy_type": "general", "code": "def generate_signals(df, symbol, timeframe, parameters):\n    \"\"\"\n    Generate trading signals based on EMA crossover\n    \n    Args:\n        df (pandas.DataFrame): Candle data\n        symbol (str): Symbol being processed\n        timeframe (str): Timeframe being processed\n        parameters (dict): Strategy parameters\n    \n    Returns:\n        list: List of trading signals\n    \"\"\"\n    # Get parameters\n    fast_ema = parameters.get(\"fast_ema\", 9)\n    slow_ema = parameters.get(\"slow_ema\", 21)\n    atr_period = parameters.get(\"atr_period\", 14)\n    atr_multiplier = parameters.get(\"atr_multiplier\", 2.0)\n    \n    # Calculate indicators\n    df[\"fast_ema\"] = df[\"close\"].ewm(span=fast_ema, adjust=False).mean()\n    df[\"slow_ema\"] = df[\"close\"].ewm(span=slow_ema, adjust=False).mean()\n    \n    # Calculate ATR for stop loss\n    df[\"high_low\"] = df[\"high\"] - df[\"low\"]\n    df[\"high_close\"] = abs(df[\"high\"] - df[\"close\"].shift())\n    df[\"low_close\"] = abs(df[\"low\"] - df[\"close\"].shift())\n    df[\"tr\"] = df[[\"high_low\", \"high_close\", \"low_close\"]].max(axis=1)\n    df[\"atr\"] = df[\"tr\"].rolling(window=atr_period).mean()\n    \n    # Generate signals\n    signals = []\n    \n    # Check only the last 3 candles for new signals\n    for i in range(len(df) - 3, len(df)):\n        # Skip if not enough data\n        if i <= 0:\n            continue\n        \n        # Buy signal: fast EMA crosses above slow EMA\n        if df[\"fast_ema\"].iloc[i-1] <= df[\"slow_ema\"].iloc[i-1] and df[\"fast_ema\"].iloc[i] > df[\"slow_ema\"].iloc[i]:\n            entry_price = df[\"close\"].iloc[i]\n            stop_loss = entry_price - (df[\"atr\"].iloc[i] * atr_multiplier)\n            take_profit = entry_price + (df[\"atr\"].iloc[i] * atr_multiplier * 1.5)\n            \n            signals.append({\n                \"symbol\": symbol,\n                \"timeframe\": timeframe,\n                \"direction\": \"buy\",\n                \"entry_price\": entry_price,\n                \"stop_loss\": stop_loss,\n                \"take_profit\": take_profit,\n                \"notes\": f\"EMA Crossover: {fast_ema} EMA crossed above {slow_ema} EMA\"\n            })\n        \n        # Sell signal: fast EMA crosses below slow EMA\n        elif df[\"fast_ema\"].iloc[i-1] >= df[\"slow_ema\"].iloc[i-1] and df[\"fast_ema\"].iloc[i] < df[\"slow_ema\"].iloc[i]:\n            entry_price = df[\"close\"].iloc[i]\n            stop_loss = entry_price + (df[\"atr\"].iloc[i] * atr_multiplier)\n            take_profit = entry_price - (df[\"atr\"].iloc[i] * atr_multiplier * 1.5)\n            \n            signals.append({\n                \"symbol\": symbol,\n                \"timeframe\": timeframe,\n                \"direction\": \"sell\",\n                \"entry_price\": entry_price,\n                \"stop_loss\": stop_loss,\n                \"take_profit\": take_profit,\n                \"notes\": f\"EMA Crossover: {fast_ema} EMA crossed below {slow_ema} EMA\"\n            })\n    \n    return signals", "active": true, "created_at": "2025-07-08T19:37:36.225670", "updated_at": "2025-07-08T20:10:28.867018"}], "prompt_templates": [{"id": 1, "post_type": "signals", "language": "fa", "template_content": "Create a professional trading signal post in this exact format:\n\nSignal Details:\nSymbol: {symbol}\nDirection: {direction}\nEntry Price: {entry_price}\nStop Loss: {stop_loss}\nTake Profit 1: {take_profit_1}\nTake Profit 2: {take_profit_2}\nTake Profit 3: {take_profit_3}\nNotes: {notes}\nChannel Brand: {channel_brand}\n\nCreate the signal in this EXACT format (translate to {language} but keep the structure):\n\n🚨 سیگنال اختصاصی اعضای کانال 🚨\n\n💎 نماد: {symbol}\n\n📈 {direction}\n\n🌩 اهرم: 10X\n\n💵 سرمایه ورود: 5%\n\n📍 نقطه ورود: {entry_price}\n\n💵 اهداف:\n🎯 هدف اول (TP1): {take_profit_1} - {tp1_percentage}% پوزیشن{take_profit_2_line}{take_profit_3_line}\n\n😀 حد ضرر: {stop_loss}\n\n⚠️ مدیریت سرمایه و کنترل ریسک اولین قدم برای موفقیت است، لطف<lemma>\n\n🔗@{channel_handle}\n\n#signal #{channel_brand} #{symbol}\n\nIMPORTANT:\n- Use ONLY the format shown above\n- The output should be in Farsi", "description": "Unified Farsi template for signals posts", "active": true, "created_at": null, "updated_at": "2025-07-08T20:00:14.649079"}, {"id": 2, "post_type": "signals", "language": "en", "template_content": "Create a professional trading signal post in this exact format:\n\nSignal Details:\nSymbol: {symbol}\nDirection: {direction}\nEntry Price: {entry_price}\nStop Loss: {stop_loss}\nTake Profit: {take_profit}\nNotes: {notes}\nChannel Brand: {channel_brand}\n\nCreate the signal in this EXACT format (translate to {language} but keep the structure):\n\n🚨 Exclusive Signal for Channel Members 🚨\n\n💎 Symbol: {symbol}\n\n📈 {direction}\n\n🌩 Leverage: 10X\n\n💵 Entry Capital: 5%\n\n📍 Entry Point: {entry_price}\n\n💵 Targets:\n💰 First Target: {take_profit}\n\n😀 Stop Loss: {stop_loss}\n\n⚠️ Capital management and risk control is the first step to success, please observe\n\n🔗@{channel_handle}\n\nIMPORTANT:\n- Use ONLY the format shown above\n- NO greetings or conversational text\n- Keep it professional and direct\n- Use appropriate emojis as shown\n- Include leverage and risk management advice\n- Language: {language}", "description": "Unified English template for signals posts", "active": true, "created_at": null, "updated_at": null}, {"id": 3, "post_type": "signals", "language": "ar", "template_content": "Create a professional trading signal post in this exact format:\n\nSignal Details:\nSymbol: {symbol}\nDirection: {direction}\nEntry Price: {entry_price}\nStop Loss: {stop_loss}\nTake Profit: {take_profit}\nNotes: {notes}\nChannel Brand: {channel_brand}\n\nCreate the signal in this EXACT format (translate to {language} but keep the structure):\n\n🚨 Exclusive Signal for Channel Members 🚨\n\n💎 Symbol: {symbol}\n\n📈 {direction}\n\n🌩 Leverage: 10X\n\n💵 Entry Capital: 5%\n\n📍 Entry Point: {entry_price}\n\n💵 Targets:\n💰 First Target: {take_profit}\n\n😀 Stop Loss: {stop_loss}\n\n⚠️ Capital management and risk control is the first step to success, please observe\n\n🔗@{channel_handle}\n\nIMPORTANT:\n- Use ONLY the format shown above\n- NO greetings or conversational text\n- Keep it professional and direct\n- Use appropriate emojis as shown\n- Include leverage and risk management advice\n- Language: {language}", "description": "Unified Arabic template for signals posts", "active": true, "created_at": null, "updated_at": null}, {"id": 4, "post_type": "news", "language": "fa", "template_content": "You are a professional financial content creator for \"{channel_brand}\".\nCreate an ORIGINAL trading news post tailored specifically for this channel's audience.\n\nNews Information:\nTitle: {title}\nContent: {content}\nSource: {source}\nLanguage: {language}\nChannel Brand: {channel_brand}\n\nIMPORTANT: Create UNIQUE content for this specific channel. Do NOT copy from other channels or use templates.\n\nFORMAT STRUCTURE (create original content following this pattern):\n\n[Write a compelling, original headline about this news with relevant emoji at the end]\n\n[Write 2-3 original sentences analyzing the market impact, include specific data/prices from the news, place ONE relevant emoji in the middle of this paragraph]\n\n🔔 Stay with us\n\n📱 @{channel_handle}\n\nCONTENT REQUIREMENTS:\n- the generated content shouldn't sound robotic or contain chinese or characters that doesnt mean in farsi, \n- Create ORIGINAL headline that captures the key market impact\n- Write UNIQUE analysis focusing on trading implications\n- Include specific numbers, percentages, or price levels from the news\n- Tailor the tone and focus to \"{channel_brand}\" brand\n- Use relevant emojis: 🛢️💰💵💶₿📊📈📉🏦⚡🌍\n- Language: {language}\n- Be concise but informative (2-3 sentences max in main content)\n\nRemember: This content represents \"{channel_brand}\" - make it unique and valuable for their specific audience.", "description": "Unified Farsi template for news posts", "active": true, "created_at": null, "updated_at": "2025-07-07T16:01:35.429417"}, {"id": 5, "post_type": "news", "language": "en", "template_content": "You are a professional financial content creator for \"{channel_brand}\".\nCreate an ORIGINAL trading news post tailored specifically for this channel's audience.\n\nNews Information:\nTitle: {title}\nContent: {content}\nSource: {source}\nLanguage: {language}\nChannel Brand: {channel_brand}\n\nIMPORTANT: Create UNIQUE content for this specific channel. Do NOT copy from other channels or use templates.\n\nFORMAT STRUCTURE (create original content following this pattern):\n\n[Write a compelling, original headline about this news with relevant emoji at the end]\n\n[Write 2-3 original sentences analyzing the market impact, include specific data/prices from the news, place ONE relevant emoji in the middle of this paragraph]\n\n🔔 Stay with us\n\n📱 @{channel_handle}\n\nCONTENT REQUIREMENTS:\n- Create ORIGINAL headline that captures the key market impact\n- Write UNIQUE analysis focusing on trading implications\n- Include specific numbers, percentages, or price levels from the news\n- <PERSON>lor the tone and focus to \"{channel_brand}\" brand\n- Use relevant emojis: 🛢️💰💵💶₿📊📈📉🏦⚡🌍\n- Language: {language}\n- Be concise but informative (2-3 sentences max in main content)\n\nRemember: This content represents \"{channel_brand}\" - make it unique and valuable for their specific audience.", "description": "Unified English template for news posts", "active": true, "created_at": null, "updated_at": null}, {"id": 6, "post_type": "news", "language": "ar", "template_content": "You are a professional financial content creator for \"{channel_brand}\".\nCreate an ORIGINAL trading news post tailored specifically for this channel's audience.\n\nNews Information:\nTitle: {title}\nContent: {content}\nSource: {source}\nLanguage: {language}\nChannel Brand: {channel_brand}\n\nIMPORTANT: Create UNIQUE content for this specific channel. Do NOT copy from other channels or use templates.\n\nFORMAT STRUCTURE (create original content following this pattern):\n\n[Write a compelling, original headline about this news with relevant emoji at the end]\n\n[Write 2-3 original sentences analyzing the market impact, include specific data/prices from the news, place ONE relevant emoji in the middle of this paragraph]\n\n🔔 Stay with us\n\n📱 @{channel_handle}\n\nCONTENT REQUIREMENTS:\n- Create ORIGINAL headline that captures the key market impact\n- Write UNIQUE analysis focusing on trading implications\n- Include specific numbers, percentages, or price levels from the news\n- <PERSON>lor the tone and focus to \"{channel_brand}\" brand\n- Use relevant emojis: 🛢️💰💵💶₿📊📈📉🏦⚡🌍\n- Language: {language}\n- Be concise but informative (2-3 sentences max in main content)\n\nRemember: This content represents \"{channel_brand}\" - make it unique and valuable for their specific audience.", "description": "Unified Arabic template for news posts", "active": true, "created_at": null, "updated_at": null}, {"id": 7, "post_type": "events", "language": "fa", "template_content": "You are a professional economic analyst for \"{channel_brand}\".\nCreate an ORIGINAL economic event analysis tailored specifically for this channel's trading audience.\n\nEvent Information:\nTitle: {title}\nCountry: {country}\nCurrency: {currency}\nPrevious: {previous}\nForecast: {forecast}\nActual: {actual}\nImpact Level: {impact_level}\nLanguage: {language}\nChannel Brand: {channel_brand}\n\nIMPORTANT: Create UNIQUE analysis for this specific channel. Do NOT use generic templates.\n\nFORMAT STRUCTURE (create original content following this pattern):\n\n{country_flag} [Write original event title] {relevant_emoji}\n\n[Write 2-3 original sentences analyzing this event's market impact. Include specific data comparison (Previous vs Forecast vs Actual if available). Explain currency implications and trading opportunities. Place ONE relevant emoji in the middle of this analysis.]\n\n🔔 Stay with us\n\n📱 @{channel_handle}\n\nANALYSIS REQUIREMENTS:\n- Write ORIGINAL 2-3 sentence analysis (not just 6 words)\n- Compare actual vs forecast vs previous values when available\n- Explain specific impact on {currency} and related markets\n- Discuss trading implications and market sentiment\n- Include specific numbers and percentages from the event data\n- Tailor analysis tone to \"{channel_brand}\" audience\n- Use relevant emojis: 👥📈🏭🏦🌍🛒📊💰⚡\n- Language: {language}\n- add #event #forexfactory at proper place\n\nRemember: This analysis represents \"{channel_brand}\" expertise - make it insightful and valuable for traders.", "description": "Unified Farsi template for events posts", "active": true, "created_at": null, "updated_at": "2025-07-07T15:56:17.896873"}, {"id": 8, "post_type": "events", "language": "en", "template_content": "You are a professional economic analyst for \"{channel_brand}\".\nCreate an ORIGINAL economic event analysis tailored specifically for this channel's trading audience.\n\nEvent Information:\nTitle: {title}\nCountry: {country}\nCurrency: {currency}\nPrevious: {previous}\nForecast: {forecast}\nActual: {actual}\nImpact Level: {impact_level}\nLanguage: {language}\nChannel Brand: {channel_brand}\n\nIMPORTANT: Create UNIQUE analysis for this specific channel. Do NOT use generic templates.\n\nFORMAT STRUCTURE (create original content following this pattern):\n\n{country_flag} [Write original event title] {relevant_emoji}\n\n[Write 2-3 original sentences analyzing this event's market impact. Include specific data comparison (Previous vs Forecast vs Actual if available). Explain currency implications and trading opportunities. Place ONE relevant emoji in the middle of this analysis.]\n\n🔔 Stay with us\n\n📱 @{channel_handle}\n\nANALYSIS REQUIREMENTS:\n- Write ORIGINAL 2-3 sentence analysis (not just 6 words)\n- Compare actual vs forecast vs previous values when available\n- Explain specific impact on {currency} and related markets\n- Discuss trading implications and market sentiment\n- Include specific numbers and percentages from the event data\n- Tailor analysis tone to \"{channel_brand}\" audience\n- Use relevant emojis: 👥📈🏭🏦🌍🛒📊💰⚡\n- Language: {language}\n\nRemember: This analysis represents \"{channel_brand}\" expertise - make it insightful and valuable for traders.", "description": "Unified English template for events posts", "active": true, "created_at": null, "updated_at": null}, {"id": 9, "post_type": "events", "language": "ar", "template_content": "You are a professional economic analyst for \"{channel_brand}\".\nCreate an ORIGINAL economic event analysis tailored specifically for this channel's trading audience.\n\nEvent Information:\nTitle: {title}\nCountry: {country}\nCurrency: {currency}\nPrevious: {previous}\nForecast: {forecast}\nActual: {actual}\nImpact Level: {impact_level}\nLanguage: {language}\nChannel Brand: {channel_brand}\n\nIMPORTANT: Create UNIQUE analysis for this specific channel. Do NOT use generic templates.\n\nFORMAT STRUCTURE (create original content following this pattern):\n\n{country_flag} [Write original event title] {relevant_emoji}\n\n[Write 2-3 original sentences analyzing this event's market impact. Include specific data comparison (Previous vs Forecast vs Actual if available). Explain currency implications and trading opportunities. Place ONE relevant emoji in the middle of this analysis.]\n\n🔔 Stay with us\n\n📱 @{channel_handle}\n\nANALYSIS REQUIREMENTS:\n- Write ORIGINAL 2-3 sentence analysis (not just 6 words)\n- Compare actual vs forecast vs previous values when available\n- Explain specific impact on {currency} and related markets\n- Discuss trading implications and market sentiment\n- Include specific numbers and percentages from the event data\n- Tailor analysis tone to \"{channel_brand}\" audience\n- Use relevant emojis: 👥📈🏭🏦🌍🛒📊💰⚡\n- Language: {language}\n\nRemember: This analysis represents \"{channel_brand}\" expertise - make it insightful and valuable for traders.", "description": "Unified Arabic template for events posts", "active": true, "created_at": null, "updated_at": null}, {"id": 10, "post_type": "greeting", "language": "fa", "template_content": "Create a daily greeting message for a trading channel that includes all today's economic events.\n\nDate: {today_date}\nTime: {time_greeting}\nTotal Events: {total_events}\nChannel: {channel_brand}\nMain Currency: {main_currency}\n\nEvents Summary: {events_summary}\n\nMessage Structure:\n\n{time_greeting} Dear Traders! 📈\n📅 Economic Calendar {formatted_date}\n[List ALL events - each on one line with emoji and time]\n💡 Today focus on {main_currency} news, market might be volatile\n🚀 Be ready and trade smartly!\n\nRules:\n- Include ALL events from the summary above\n- Event format: [Impact emoji] [Event name] ([Currency]) - [Hour:Minute]\n- Use 🔴 for high impact, 🟡 for medium, 🟢 for low\n- Time format: Hour:Minute (24-hour)\n- Order by impact (high first), then by time\n- Language: {language}\n- Keep event names short but clear\n- Don't limit to 3-4 events - show all\n- Don't use \"+X other events\" - list everything\n- Translate appropriately but keep structure\n- add #economic_calendar #channel_brand at proper place", "description": "Unified Farsi template for greeting posts", "active": true, "created_at": null, "updated_at": "2025-07-07T15:57:19.195523"}, {"id": 11, "post_type": "greeting", "language": "en", "template_content": "Create a daily greeting message for a trading channel that includes all today's economic events.\n\nDate: {today_date}\nTime: {time_greeting}\nTotal Events: {total_events}\nChannel: {channel_brand}\nMain Currency: {main_currency}\n\nEvents Summary: {events_summary}\n\nMessage Structure:\n\n{time_greeting} Dear Traders! 📈\n📅 Economic Calendar {formatted_date}\n[List ALL events - each on one line with emoji and time]\n💡 Today focus on {main_currency} news, market might be volatile\n🚀 Be ready and trade smartly!\n\nRules:\n- Include ALL events from the summary above\n- Event format: [Impact emoji] [Event name] ([Currency]) - [Hour:Minute]\n- Use 🔴 for high impact, 🟡 for medium, 🟢 for low\n- Time format: Hour:Minute (24-hour)\n- Order by impact (high first), then by time\n- Language: {language}\n- Keep event names short but clear\n- Don't limit to 3-4 events - show all\n- Don't use \"+X other events\" - list everything\n- Translate appropriately but keep structure", "description": "Unified English template for greeting posts", "active": true, "created_at": null, "updated_at": null}, {"id": 12, "post_type": "greeting", "language": "ar", "template_content": "Create a daily greeting message for a trading channel that includes all today's economic events.\n\nDate: {today_date}\nTime: {time_greeting}\nTotal Events: {total_events}\nChannel: {channel_brand}\nMain Currency: {main_currency}\n\nEvents Summary: {events_summary}\n\nMessage Structure:\n\n{time_greeting} Dear Traders! 📈\n📅 Economic Calendar {formatted_date}\n[List ALL events - each on one line with emoji and time]\n💡 Today focus on {main_currency} news, market might be volatile\n🚀 Be ready and trade smartly!\n\nRules:\n- Include ALL events from the summary above\n- Event format: [Impact emoji] [Event name] ([Currency]) - [Hour:Minute]\n- Use 🔴 for high impact, 🟡 for medium, 🟢 for low\n- Time format: Hour:Minute (24-hour)\n- Order by impact (high first), then by time\n- Language: {language}\n- Keep event names short but clear\n- Don't limit to 3-4 events - show all\n- Don't use \"+X other events\" - list everything\n- Translate appropriately but keep structure", "description": "Unified Arabic template for greeting posts", "active": true, "created_at": null, "updated_at": null}, {"id": 13, "post_type": "market_analysis", "language": "fa", "template_content": "Generate a very short market analysis  for {symbol} {timeframe}.\n\nCurrent: {current_price} ({change_str})\n\nFormat:\nAnalysis of {symbol} - {timeframe}\n📊 {symbol} {timeframe} | {current_price}\n📈 [Trend direction in 2-3 words]\n🎯 [Key level or target]\n💡 [2-3 sentence analysis]\n\n@{channel_handler}\n\nLanguage: {language}\nKeep under 200 characters total. NO greetings or explanations.\n- add #analysis #symbol #timeframe at proper place", "description": "Unified Farsi template for market_analysis posts", "active": true, "created_at": null, "updated_at": "2025-07-07T16:00:35.278866"}, {"id": 14, "post_type": "market_analysis", "language": "en", "template_content": "Generate a very short market analysis (max 4 lines) for {symbol} {timeframe}.\n\nCurrent: {current_price} ({change_str})\n\nFormat:\n📊 {symbol} {timeframe} | {current_price}\n📈 [Trend direction in 2-3 words]\n🎯 [Key level or target]\n💡 [Brief outlook]\n\nLanguage: {language}\nKeep under 200 characters total. NO greetings or explanations.", "description": "Unified English template for market_analysis posts", "active": true, "created_at": null, "updated_at": null}, {"id": 15, "post_type": "market_analysis", "language": "ar", "template_content": "Generate a very short market analysis (max 4 lines) for {symbol} {timeframe}.\n\nCurrent: {current_price} ({change_str})\n\nFormat:\n📊 {symbol} {timeframe} | {current_price}\n📈 [Trend direction in 2-3 words]\n🎯 [Key level or target]\n💡 [Brief outlook]\n\nLanguage: {language}\nKeep under 200 characters total. NO greetings or explanations.", "description": "Unified Arabic template for market_analysis posts", "active": true, "created_at": null, "updated_at": null}, {"id": 16, "post_type": "countdown", "language": "fa", "template_content": "Create a countdown post with detailed analysis for \"{channel_brand}\".\n\nEvent Information:\nTitle: {title}\nCountry: {country}\nCurrency: {currency}\nPrevious: {previous}\nForecast: {forecast}\nMinutes until: {minutes_until}\nLanguage: {language}\nChannel Brand: {channel_brand}\n\nFORMAT STRUCTURE:\n⏰ {country_flag} [Event Title] in {minutes_until}min {impact_emoji}\n\n📊 Previous: {previous} | Forecast: {forecast}\n\n[Write 2-3 sentences analyzing the expected market impact of this event. Explain what traders should watch for, potential currency movements, and market implications. Include ONE relevant emoji in the middle.]\n\n📱 @{channel_handle}\n\nANALYSIS REQUIREMENTS:\n- Write 2-3 sentences of meaningful analysis (not just 6 words)\n- Explain what to expect from this event based on forecast vs previous\n- Discuss potential {currency} impact and market movements\n- Mention key levels or scenarios traders should watch\n- <PERSON>lor analysis to \"{channel_brand}\" trading audience\n- Language: {language}\n- Keep it concise but informative\n- add #event #countdown #forexfactory at proper place\n\nRemember: Provide valuable pre-event analysis that helps traders prepare.", "description": "Unified Farsi template for countdown posts", "active": true, "created_at": null, "updated_at": "2025-07-07T15:55:06.591349"}, {"id": 17, "post_type": "countdown", "language": "en", "template_content": "Create a countdown post with detailed analysis for \"{channel_brand}\".\n\nEvent Information:\nTitle: {title}\nCountry: {country}\nCurrency: {currency}\nPrevious: {previous}\nForecast: {forecast}\nMinutes until: {minutes_until}\nLanguage: {language}\nChannel Brand: {channel_brand}\n\nFORMAT STRUCTURE:\n⏰ {country_flag} [Event Title] in {minutes_until}min {impact_emoji}\n\n📊 Previous: {previous} | Forecast: {forecast}\n\n[Write 2-3 sentences analyzing the expected market impact of this event. Explain what traders should watch for, potential currency movements, and market implications. Include ONE relevant emoji in the middle.]\n\n📱 @{channel_handle}\n\nANALYSIS REQUIREMENTS:\n- Write 2-3 sentences of meaningful analysis (not just 6 words)\n- Explain what to expect from this event based on forecast vs previous\n- Discuss potential {currency} impact and market movements\n- Mention key levels or scenarios traders should watch\n- Tailor analysis to \"{channel_brand}\" trading audience\n- Language: {language}\n- Keep it concise but informative\n\nRemember: Provide valuable pre-event analysis that helps traders prepare.", "description": "Unified English template for countdown posts", "active": true, "created_at": null, "updated_at": null}, {"id": 18, "post_type": "countdown", "language": "ar", "template_content": "Create a countdown post with detailed analysis for \"{channel_brand}\".\n\nEvent Information:\nTitle: {title}\nCountry: {country}\nCurrency: {currency}\nPrevious: {previous}\nForecast: {forecast}\nMinutes until: {minutes_until}\nLanguage: {language}\nChannel Brand: {channel_brand}\n\nFORMAT STRUCTURE:\n⏰ {country_flag} [Event Title] in {minutes_until}min {impact_emoji}\n\n📊 Previous: {previous} | Forecast: {forecast}\n\n[Write 2-3 sentences analyzing the expected market impact of this event. Explain what traders should watch for, potential currency movements, and market implications. Include ONE relevant emoji in the middle.]\n\n📱 @{channel_handle}\n\nANALYSIS REQUIREMENTS:\n- Write 2-3 sentences of meaningful analysis (not just 6 words)\n- Explain what to expect from this event based on forecast vs previous\n- Discuss potential {currency} impact and market movements\n- Mention key levels or scenarios traders should watch\n- Tailor analysis to \"{channel_brand}\" trading audience\n- Language: {language}\n- Keep it concise but informative\n\nRemember: Provide valuable pre-event analysis that helps traders prepare.", "description": "Unified Arabic template for countdown posts", "active": true, "created_at": null, "updated_at": null}, {"id": 19, "post_type": "signal_update", "language": "fa", "template_content": "Create a professional trading performance update post in this exact format:\n\nPerformance Update Details:\nSymbol: {symbol}\nDirection: {direction}\nEntry Price: {entry_price}\nExit Price: {exit_price}\nProfit/Loss: {profit_loss}\nStatus: {status}\nTP Level: {tp_level_text}\nBreak Even: {break_even_message}\nChannel Brand: {channel_brand}\n\nCreate the performance update in this EXACT format (translate to {language} but keep the structure):\n\n{result_emoji} نتیجه معامله {result_emoji}\n\n💎 نماد: {symbol}\n\n📈 جهت: {direction}\n\n📍 قیمت ورود: {entry_price}\n📍 قیمت خروج: {exit_price}\n\n💰 سود/زیان: {profit_loss}\n\n🎯 وضعیت: {tp_level_text} {status}\n\n{break_even_message}\n\nاهداف اصلی:\n🎯 هدف اول (TP1): {take_profit_1} - {tp1_percentage}% پوزیشن{take_profit_2_line}{take_profit_3_line}\n\n🔗@{channel_handle}\n\n#performance #{channel_brand} #{symbol}\n\nIMPORTANT:\n- Use ONLY the format shown above\n- Show appropriate emoji based on profit/loss result\n- Include TP level information if available\n- Show break-even message if applicable\n- The output should be in Farsi\n- Translate all text except trading terms and symbols", "description": "Unified Farsi template for signal_update posts", "active": true, "created_at": null, "updated_at": "2025-07-08T20:09:02.138335"}, {"id": 20, "post_type": "signal_update", "language": "en", "template_content": "Create a very short trade result post following crypto signal style:\n\n<h1> Signal Update </h1>\nSignal: {symbol}\nDirection: {direction}\nEntry: {entry_price}\nExit: {exit_price}\nResult: {profit_loss}\nStatus: {status}\nChannel Brand: {channel_brand}\n\nEXACT format:\n{result_emoji} TRADE RESULT {result_emoji}\n➖➖➖➖➖➖➖➖➖➖➖➖➖\nCOIN: {symbol} 🐃\n{direction} | {profit_loss}\n\n📍 ENTRY: {entry_price}\n📍 EXIT: {exit_price}\n\nCHANNEL ID @{channel_handle} ✅\n\nRULES:\n- Use ONLY the format above\n- NO greetings or extra text\n- Keep it professional and direct\n- Maximum 4 digits for prices\n- Language: {language}\n- add these hashtags in proper place(#{channel_handle}, #analysis ,#signal", "description": "Unified English template for signal_update posts", "active": true, "created_at": null, "updated_at": "2025-07-07T15:51:53.246832"}, {"id": 21, "post_type": "signal_update", "language": "ar", "template_content": "Create a very short trade result post following crypto signal style:\n\nSignal: {symbol}\nDirection: {direction}\nEntry: {entry_price}\nExit: {exit_price}\nResult: {profit_loss}\nStatus: {status}\nChannel Brand: {channel_brand}\n\nEXACT format:\n{result_emoji} TRADE RESULT {result_emoji}\n➖➖➖➖➖➖➖➖➖➖➖➖➖\nCOIN: {symbol} 🐃\n{direction} | {profit_loss}\n\n📍 ENTRY: {entry_price}\n📍 EXIT: {exit_price}\n\nCHANNEL ID @{channel_handle} ✅\n\nRULES:\n- Use ONLY the format above\n- NO greetings or extra text\n- Keep it professional and direct\n- Maximum 4 digits for prices\n- Language: {language}", "description": "Unified Arabic template for signal_update posts", "active": true, "created_at": null, "updated_at": null}, {"id": 22, "post_type": "event_result", "language": "fa", "template_content": "You are a professional economic analyst for \"{channel_brand}\".\nCreate an IMMEDIATE REACTION analysis for this just-released economic data.\n\nEvent Information:\nTitle: {title}\nCountry: {country}\nCurrency: {currency}\nPrevious: {previous}\nForecast: {forecast}\nACTUAL: {actual} ⚡ JUST RELEASED\nImpact Level: {impact_level}\nLanguage: {language}\n\nFORMAT STRUCTURE:\n🚨 {country_flag} [Event Title] RELEASED {impact_emoji}\n\n📊 Actual: {actual} | Forecast: {forecast} | Previous: {previous}\n\n[Write 2-3 sentences analyzing the immediate market impact. Compare actual vs forecast. Explain what this means for {currency} and trading opportunities.]\n\n⚡ Live Analysis\n📱 @{channel_handle}\n\nREQUIREMENTS:\n- Focus on IMMEDIATE market reaction\n- Compare actual vs forecast vs previous\n- Explain currency impact and trading implications\n- Language: {language}\n- add #event #result #forexfactory #analysis at proper place", "description": "Unified Farsi template for event_result posts", "active": true, "created_at": null, "updated_at": "2025-07-08T06:50:31.547306"}, {"id": 23, "post_type": "event_result", "language": "en", "template_content": "You are a professional economic analyst for \"{channel_brand}\".\nCreate an IMMEDIATE REACTION analysis for this just-released economic data.\n\nEvent Information:\nTitle: {title}\nCountry: {country}\nCurrency: {currency}\nPrevious: {previous}\nForecast: {forecast}\nACTUAL: {actual} ⚡ JUST RELEASED\nImpact Level: {impact_level}\nLanguage: {language}\n\nFORMAT STRUCTURE:\n🚨 {country_flag} [Event Title] RELEASED {impact_emoji}\n\n📊 Actual: {actual} | Forecast: {forecast} | Previous: {previous}\n\n[Write 2-3 sentences analyzing the immediate market impact. Compare actual vs forecast. Explain what this means for {currency} and trading opportunities.]\n\n⚡ Live Analysis\n📱 @{channel_handle}\n\nREQUIREMENTS:\n- Focus on IMMEDIATE market reaction\n- Compare actual vs forecast vs previous\n- Explain currency impact and trading implications\n- Keep under 200 characters total\n- Language: {language}", "description": "Unified English template for event_result posts", "active": true, "created_at": null, "updated_at": null}, {"id": 24, "post_type": "event_result", "language": "ar", "template_content": "You are a professional economic analyst for \"{channel_brand}\".\nCreate an IMMEDIATE REACTION analysis for this just-released economic data.\n\nEvent Information:\nTitle: {title}\nCountry: {country}\nCurrency: {currency}\nPrevious: {previous}\nForecast: {forecast}\nACTUAL: {actual} ⚡ JUST RELEASED\nImpact Level: {impact_level}\nLanguage: {language}\n\nFORMAT STRUCTURE:\n🚨 {country_flag} [Event Title] RELEASED {impact_emoji}\n\n📊 Actual: {actual} | Forecast: {forecast} | Previous: {previous}\n\n[Write 2-3 sentences analyzing the immediate market impact. Compare actual vs forecast. Explain what this means for {currency} and trading opportunities.]\n\n⚡ Live Analysis\n📱 @{channel_handle}\n\nREQUIREMENTS:\n- Focus on IMMEDIATE market reaction\n- Compare actual vs forecast vs previous\n- Explain currency impact and trading implications\n- Keep under 200 characters total\n- Language: {language}", "description": "Unified Arabic template for event_result posts", "active": true, "created_at": null, "updated_at": null}], "channels": [{"id": 1, "chat_id": "-1002540316704", "name": "Test Channel", "description": "Test channel for MignalyBot", "language": "fa", "active": true, "brand_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "brand_description": "AI-Powered Trading Bot", "enable_advertisement": true, "advertisement_text": "This message generated by <PERSON><PERSON><PERSON>", "advertisement_url": "https://mignaly.com", "post_types": "news,signal,analysis,event,performance,greeting", "enable_date_stickers": true, "date_sticker_style": "modern", "created_at": "2025-07-06T07:46:01.117744", "updated_at": "2025-07-06T08:12:08.312204"}], "statistics": {"strategies_count": 6, "templates_count": 24, "channels_count": 1}}