"""
Enhanced Qwen AI client for MignalyBot v2

Features:
- Connection pooling and reuse
- Rate limiting and retry logic
- Response caching
- Performance monitoring
- Error handling and recovery
- Streaming support
"""

import asyncio
import hashlib
import json
import logging
import time
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta
import httpx

from core.config.settings import AISettings
from core.exceptions.base import <PERSON><PERSON>IEx<PERSON>, handle_exception
from core.utils.helpers import RateLimiter, retry_with_backoff, async_performance_monitor
from ai.cache.manager import CacheManager


class QwenClient:
    """Enhanced Qwen AI client with optimizations"""
    
    def __init__(self, settings: AISettings, cache_manager: Optional[CacheManager] = None):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        self.cache_manager = cache_manager
        
        # Rate limiting
        self.rate_limiter = RateLimiter(
            max_calls=settings.rate_limit_requests,
            window_seconds=settings.rate_limit_window
        )
        
        # HTTP client with connection pooling
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(settings.timeout),
            limits=httpx.Limits(max_connections=10, max_keepalive_connections=5),
            headers={
                "Authorization": f"Bearer {settings.qwen_api_key}",
                "Content-Type": "application/json"
            }
        )
        
        # Performance tracking
        self.stats = {
            'total_requests': 0,
            'cached_responses': 0,
            'failed_requests': 0,
            'avg_response_time': 0.0,
            'total_tokens_used': 0
        }
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()
    
    def _generate_cache_key(self, prompt: str, **kwargs) -> str:
        """Generate cache key for request"""
        cache_data = {
            'prompt': prompt,
            'model': self.settings.qwen_model,
            'temperature': self.settings.temperature,
            'max_tokens': self.settings.max_tokens,
            **kwargs
        }
        cache_string = json.dumps(cache_data, sort_keys=True)
        return hashlib.md5(cache_string.encode()).hexdigest()
    
    def _update_stats(self, response_time: float, tokens_used: int = 0, cached: bool = False, failed: bool = False):
        """Update performance statistics"""
        self.stats['total_requests'] += 1
        
        if cached:
            self.stats['cached_responses'] += 1
        
        if failed:
            self.stats['failed_requests'] += 1
        
        if tokens_used:
            self.stats['total_tokens_used'] += tokens_used
        
        # Update average response time
        total_requests = self.stats['total_requests']
        current_avg = self.stats['avg_response_time']
        self.stats['avg_response_time'] = (
            (current_avg * (total_requests - 1) + response_time) / total_requests
        )
    
    @async_performance_monitor("qwen_api_call")
    @retry_with_backoff(max_retries=3, base_delay=1.0)
    async def _make_request(self, messages: List[Dict[str, str]], **kwargs) -> Dict[str, Any]:
        """Make API request with rate limiting and error handling"""
        # Wait for rate limit
        await self.rate_limiter.wait_for_slot()
        
        # Prepare request data
        request_data = {
            "model": self.settings.qwen_model,
            "messages": messages,
            "temperature": kwargs.get('temperature', self.settings.temperature),
            "max_tokens": kwargs.get('max_tokens', self.settings.max_tokens),
            "stream": kwargs.get('stream', False)
        }
        
        start_time = time.time()
        
        try:
            response = await self.client.post(
                f"{self.settings.qwen_endpoint}/chat/completions",
                json=request_data
            )
            
            response_time = time.time() - start_time
            
            if response.status_code != 200:
                raise AIAPIException(
                    f"Qwen API request failed with status {response.status_code}",
                    api_endpoint=self.settings.qwen_endpoint,
                    status_code=response.status_code,
                    context={'response_text': response.text}
                )
            
            result = response.json()
            
            # Extract token usage
            tokens_used = 0
            if 'usage' in result:
                tokens_used = result['usage'].get('total_tokens', 0)
            
            self._update_stats(response_time, tokens_used)
            
            return result
            
        except httpx.TimeoutException as e:
            self._update_stats(time.time() - start_time, failed=True)
            raise AIAPIException(
                "Qwen API request timed out",
                api_endpoint=self.settings.qwen_endpoint,
                original_exception=e
            )
        except httpx.RequestError as e:
            self._update_stats(time.time() - start_time, failed=True)
            raise AIAPIException(
                f"Qwen API request failed: {str(e)}",
                api_endpoint=self.settings.qwen_endpoint,
                original_exception=e
            )
    
    async def generate_completion(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        use_cache: bool = True,
        **kwargs
    ) -> str:
        """
        Generate AI completion with caching
        
        Args:
            prompt: User prompt
            system_prompt: Optional system prompt
            use_cache: Whether to use caching
            **kwargs: Additional parameters
            
        Returns:
            Generated text completion
        """
        # Check cache first
        if use_cache and self.cache_manager:
            cache_key = self._generate_cache_key(prompt, system_prompt=system_prompt, **kwargs)
            cached_response = await self.cache_manager.get(cache_key)
            
            if cached_response:
                self._update_stats(0, cached=True)
                return cached_response
        
        # Prepare messages
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})
        
        try:
            # Make API request
            response = await self._make_request(messages, **kwargs)
            
            # Extract content
            if 'choices' in response and response['choices']:
                content = response['choices'][0]['message']['content']
                
                # Cache the response
                if use_cache and self.cache_manager:
                    await self.cache_manager.set(
                        cache_key,
                        content,
                        ttl=self.settings.cache_ttl
                    )
                
                return content
            else:
                raise AIAPIException(
                    "Invalid response format from Qwen API",
                    context={'response': response}
                )
                
        except Exception as e:
            handle_exception(e, self.logger, "qwen_generate_completion", reraise=True)
    
    async def generate_market_analysis(
        self,
        symbol: str,
        timeframe: str,
        candles: List[Any],
        language: str = "en",
        channel_brand: Optional[str] = None
    ) -> str:
        """Generate market analysis for a symbol"""
        # Prepare candle data
        candle_data = []
        for candle in candles[:20]:  # Limit to last 20 candles
            candle_data.append({
                "timestamp": candle.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                "open": float(candle.open),
                "high": float(candle.high),
                "low": float(candle.low),
                "close": float(candle.close),
                "volume": float(candle.volume)
            })
        
        # Language-specific prompts
        if language == "fa":
            system_prompt = f"""شما یک تحلیلگر مالی حرفه‌ای هستید که برای کانال {channel_brand or 'MignalyBot'} تحلیل بازار تولید می‌کنید.
تحلیل جامع و دقیقی از داده‌های قیمت ارائه دهید که شامل روندها، سطوح حمایت و مقاومت، و پیش‌بینی‌های کوتاه مدت باشد."""
            
            prompt = f"""لطفاً تحلیل کاملی از {symbol} در تایم فریم {timeframe} ارائه دهید.

داده‌های کندل اخیر:
{json.dumps(candle_data, indent=2)}

تحلیل شما باید شامل موارد زیر باشد:
1. تحلیل روند کلی
2. سطوح کلیدی حمایت و مقاومت
3. اندیکاتورهای تکنیکال
4. پیش‌بینی کوتاه مدت
5. نکات مهم برای معامله‌گران

لطفاً پاسخ را به زبان فارسی و با استفاده از اموجی مناسب ارائه دهید."""
        else:
            system_prompt = f"""You are a professional financial analyst creating market analysis for {channel_brand or 'MignalyBot'} channel.
Provide comprehensive and accurate analysis of price data including trends, support/resistance levels, and short-term predictions."""
            
            prompt = f"""Please provide a comprehensive analysis of {symbol} on {timeframe} timeframe.

Recent candle data:
{json.dumps(candle_data, indent=2)}

Your analysis should include:
1. Overall trend analysis
2. Key support and resistance levels
3. Technical indicators
4. Short-term prediction
5. Important notes for traders

Please provide the response in English with appropriate emojis."""
        
        return await self.generate_completion(
            prompt=prompt,
            system_prompt=system_prompt,
            temperature=0.7,
            max_tokens=1500
        )
    
    async def generate_news_analysis(
        self,
        news_title: str,
        news_content: str,
        symbols: List[str],
        language: str = "en"
    ) -> str:
        """Generate news analysis"""
        symbols_str = ", ".join(symbols)
        
        if language == "fa":
            system_prompt = """شما یک تحلیلگر اخبار مالی هستید که تأثیر اخبار بر بازارهای مالی را تحلیل می‌کنید."""
            
            prompt = f"""لطفاً تحلیل کاملی از این خبر ارائه دهید:

عنوان: {news_title}
محتوا: {news_content}
نمادهای مرتبط: {symbols_str}

تحلیل شما باید شامل:
1. خلاصه خبر
2. تأثیر احتمالی بر بازار
3. نمادهای تحت تأثیر
4. توصیه‌های کوتاه مدت

پاسخ را به زبان فارسی و با اموجی مناسب ارائه دهید."""
        else:
            system_prompt = """You are a financial news analyst who analyzes the impact of news on financial markets."""
            
            prompt = f"""Please provide a comprehensive analysis of this news:

Title: {news_title}
Content: {news_content}
Related symbols: {symbols_str}

Your analysis should include:
1. News summary
2. Potential market impact
3. Affected symbols
4. Short-term recommendations

Provide the response in English with appropriate emojis."""
        
        return await self.generate_completion(
            prompt=prompt,
            system_prompt=system_prompt,
            temperature=0.6,
            max_tokens=1000
        )
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get client performance statistics"""
        return self.stats.copy()
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check"""
        try:
            start_time = time.time()
            
            # Simple test request
            test_response = await self.generate_completion(
                "Test message",
                use_cache=False,
                max_tokens=10
            )
            
            response_time = (time.time() - start_time) * 1000
            
            return {
                'status': 'healthy',
                'response_time_ms': response_time,
                'stats': await self.get_stats()
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e),
                'stats': await self.get_stats()
            }
