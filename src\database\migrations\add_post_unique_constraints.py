"""
Migration to add unique constraints to posts table to prevent duplicate posts
"""

import logging
from sqlalchemy import text
from src.database.setup import get_async_db, is_sqlite_db

logger = logging.getLogger(__name__)


async def add_post_unique_constraints():
    """
    Add unique constraints to the posts table to prevent duplicate posts
    for the same channel/signal, channel/event, and channel/news combinations
    """
    logger.info("Adding unique constraints to posts table...")
    
    async for db in get_async_db():
        try:
            if is_sqlite_db():
                # SQLite doesn't support adding constraints to existing tables easily
                # We need to recreate the table with constraints
                logger.info("SQLite detected - recreating posts table with constraints...")
                
                # First, create a backup table
                await db.execute(text("""
                    CREATE TABLE posts_backup AS SELECT * FROM posts
                """))
                
                # Drop the original table
                await db.execute(text("DROP TABLE posts"))
                
                # Recreate the table with constraints
                await db.execute(text("""
                    CREATE TABLE posts (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        channel_id INTEGER NOT NULL,
                        type VARCHAR(20) NOT NULL,
                        content TEXT NOT NULL,
                        status VARCHAR(20) DEFAULT 'draft',
                        scheduled_time DATETIME,
                        published_time DATETIME,
                        message_id VARCHAR(50),
                        news_id INTEGER,
                        event_id INTEGER,
                        signal_id INTEGER,
                        image_path VARCHAR(512),
                        image_url VARCHAR(512),
                        created_at DATETIME NOT NULL,
                        updated_at DATETIME NOT NULL,
                        FOREIGN KEY (channel_id) REFERENCES channels(id),
                        FOREIGN KEY (news_id) REFERENCES news_items(id),
                        FOREIGN KEY (event_id) REFERENCES economic_events(id),
                        FOREIGN KEY (signal_id) REFERENCES trading_signals(id),
                        UNIQUE (channel_id, signal_id, type) WHERE signal_id IS NOT NULL,
                        UNIQUE (channel_id, event_id, type) WHERE event_id IS NOT NULL,
                        UNIQUE (channel_id, news_id, type) WHERE news_id IS NOT NULL
                    )
                """))
                
                # Copy data back, removing duplicates
                await db.execute(text("""
                    INSERT INTO posts (
                        id, channel_id, type, content, status, scheduled_time, 
                        published_time, message_id, news_id, event_id, signal_id,
                        image_path, image_url, created_at, updated_at
                    )
                    SELECT DISTINCT
                        id, channel_id, type, content, status, scheduled_time,
                        published_time, message_id, news_id, event_id, signal_id,
                        image_path, image_url, created_at, updated_at
                    FROM posts_backup
                    WHERE id IN (
                        SELECT MIN(id) 
                        FROM posts_backup 
                        GROUP BY channel_id, 
                                 COALESCE(signal_id, 0), 
                                 COALESCE(event_id, 0), 
                                 COALESCE(news_id, 0), 
                                 type
                    )
                    ORDER BY id
                """))
                
                # Drop the backup table
                await db.execute(text("DROP TABLE posts_backup"))
                
                db.commit()
                logger.info("SQLite posts table recreated with unique constraints")
                
            else:
                # PostgreSQL - add constraints directly
                logger.info("PostgreSQL detected - adding unique constraints...")
                
                # First, remove any existing duplicate posts
                logger.info("Removing duplicate posts...")
                
                # Remove duplicate signal posts
                await db.execute(text("""
                    DELETE FROM posts 
                    WHERE id NOT IN (
                        SELECT MIN(id) 
                        FROM posts 
                        WHERE signal_id IS NOT NULL
                        GROUP BY channel_id, signal_id, type
                    ) AND signal_id IS NOT NULL
                """))
                
                # Remove duplicate event posts
                await db.execute(text("""
                    DELETE FROM posts 
                    WHERE id NOT IN (
                        SELECT MIN(id) 
                        FROM posts 
                        WHERE event_id IS NOT NULL
                        GROUP BY channel_id, event_id, type
                    ) AND event_id IS NOT NULL
                """))
                
                # Remove duplicate news posts
                await db.execute(text("""
                    DELETE FROM posts 
                    WHERE id NOT IN (
                        SELECT MIN(id) 
                        FROM posts 
                        WHERE news_id IS NOT NULL
                        GROUP BY channel_id, news_id, type
                    ) AND news_id IS NOT NULL
                """))
                
                # Add unique constraints
                try:
                    await db.execute(text("""
                        ALTER TABLE posts 
                        ADD CONSTRAINT uq_post_channel_signal_type 
                        UNIQUE (channel_id, signal_id, type)
                    """))
                    logger.info("Added unique constraint for channel_id, signal_id, type")
                except Exception as e:
                    if "already exists" in str(e).lower():
                        logger.info("Unique constraint uq_post_channel_signal_type already exists")
                    else:
                        raise
                
                try:
                    await db.execute(text("""
                        ALTER TABLE posts 
                        ADD CONSTRAINT uq_post_channel_event_type 
                        UNIQUE (channel_id, event_id, type)
                    """))
                    logger.info("Added unique constraint for channel_id, event_id, type")
                except Exception as e:
                    if "already exists" in str(e).lower():
                        logger.info("Unique constraint uq_post_channel_event_type already exists")
                    else:
                        raise
                
                try:
                    await db.execute(text("""
                        ALTER TABLE posts 
                        ADD CONSTRAINT uq_post_channel_news_type 
                        UNIQUE (channel_id, news_id, type)
                    """))
                    logger.info("Added unique constraint for channel_id, news_id, type")
                except Exception as e:
                    if "already exists" in str(e).lower():
                        logger.info("Unique constraint uq_post_channel_news_type already exists")
                    else:
                        raise
                
                await db.commit()
                logger.info("PostgreSQL unique constraints added successfully")
            
            logger.info("Post unique constraints migration completed successfully")
            return True
            
        except Exception as e:
            if is_sqlite_db():
                db.rollback()
            else:
                await db.rollback()
            logger.error(f"Error adding post unique constraints: {e}", exc_info=True)
            raise


async def remove_post_unique_constraints():
    """
    Remove unique constraints from posts table (rollback migration)
    """
    logger.info("Removing unique constraints from posts table...")
    
    async for db in get_async_db():
        try:
            if is_sqlite_db():
                logger.warning("Cannot easily remove constraints from SQLite table")
                logger.info("To rollback, you would need to recreate the table without constraints")
                return False
            else:
                # PostgreSQL - drop constraints
                try:
                    await db.execute(text("ALTER TABLE posts DROP CONSTRAINT IF EXISTS uq_post_channel_signal_type"))
                    await db.execute(text("ALTER TABLE posts DROP CONSTRAINT IF EXISTS uq_post_channel_event_type"))
                    await db.execute(text("ALTER TABLE posts DROP CONSTRAINT IF EXISTS uq_post_channel_news_type"))
                    await db.commit()
                    logger.info("PostgreSQL unique constraints removed successfully")
                except Exception as e:
                    logger.error(f"Error removing constraints: {e}")
                    raise
            
            return True
            
        except Exception as e:
            if is_sqlite_db():
                db.rollback()
            else:
                await db.rollback()
            logger.error(f"Error removing post unique constraints: {e}", exc_info=True)
            raise


if __name__ == "__main__":
    import asyncio
    
    async def main():
        await add_post_unique_constraints()
    
    asyncio.run(main())
