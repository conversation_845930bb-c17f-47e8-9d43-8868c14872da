"""
Migration script to add prompt_templates table and populate with default prompts
"""

import logging
from sqlalchemy import text
from src.database.setup import get_async_db, is_sqlite_db

logger = logging.getLogger(__name__)

# Default prompt templates based on current Farsi prompts
DEFAULT_PROMPTS = {
    "signals": {
        "fa": """Create a professional trading signal post in the exact style of these examples. DO NOT include any greetings or conversational text.

Signal Details:
Symbol: {symbol}
Direction: {direction}
Entry Price: {entry_price}
Stop Loss: {stop_loss}
Take Profit: {take_profit}
Notes: {notes}
Channel Brand: {channel_brand}

Create the signal in this EXACT format style (translate to Farsi but keep the structure):

🚨 سیگنال اختصاصی برای اعضای کانال 🚨

💎 ارز : {symbol}

📈 {direction}

🌩 لوریج: 10X

💵 میزان سرمایه ورودی: 5%

📍 نقطه ورود: {entry_price}

💵 اهداف:
💰 هدف اول: {take_profit_1}
💰 هدف دوم: {take_profit_2}
💰 هدف سوم: {take_profit_3}

😀 استاپ‌لاس : {stop_loss}

⚠️ مدیریت سرمایه و رعایت حد ضرر، اولین قدم برای موفقیت است لطفا رعایت کنید

🔗@{channel_handle}

IMPORTANT:
- Use ONLY the format shown above
- NO greetings or conversational text
- Keep it professional and direct
- Use appropriate emojis as shown
- Include leverage and risk management advice"""
    },
    
    "greeting": {
        "fa": """یک پیام خوش‌آمدگویی روزانه برای کانال تریدینگ بنویس که شامل تمام رویدادهای اقتصادی امروز باشد.

تاریخ: {today_date}
زمان: {time_greeting}
تعداد رویدادها: {total_events}
کانال: {channel_brand}
ارز اصلی: {main_currency}

خلاصه رویدادها:{events_summary}

ساختار پیام:

{time_greeting} تریدرهای عزیز! 📈
📅 تقویم اقتصادی {formatted_date}
[فهرست تمام رویدادها - هر کدام در یک خط با ایموجی و زمان مناسب]
💡 امروز روی اخبار {main_currency} تمرکز کنید، بازار ممکنه پرنوسان باشه
🚀 آماده باشید و هوشمندانه معامله کنید!

قوانین:
- تمام رویدادها رو از خلاصه بالا بیار
- فرمت رویداد: [ایموجی تاثیر] [نام رویداد] ([ارز]) - [ساعت:دقیقه]
- برای تاثیر زیاد 🔴، متوسط 🟡، کم 🟢 استفاده کن
- فرمت زمان: ساعت:دقیقه (۲۴ ساعته)
- ترتیب بر اساس تاثیر (زیاد اول)، بعد زمان
- زبان: فارسی طبیعی و ساده
- نام رویدادها رو مختصر ولی واضح بنویس
- محدود نکن به ۳-۴ رویداد - همه رو نشون بده
- از "+X رویداد دیگه" استفاده نکن - همه رو لیست کن"""
    },
    
    "news": {
        "fa": """You are a professional financial content creator for "{channel_brand}".
Create an ORIGINAL trading news post tailored specifically for this channel's audience.

News Information:
Title: {title}
Content: {content}
Source: {source}
Language: {language}
Channel Brand: {channel_brand}

IMPORTANT: Create UNIQUE content for this specific channel. Do NOT copy from other channels or use templates.

FORMAT STRUCTURE (create original content following this pattern):

[Write a compelling, original headline about this news with relevant emoji at the end]

[Write 2-3 original sentences analyzing the market impact, include specific data/prices from the news, place ONE relevant emoji in the middle of this paragraph]

🔔همراه ما باشید

📱 @{channel_handle}

CONTENT REQUIREMENTS:
- Create ORIGINAL headline that captures the key market impact
- Write UNIQUE analysis focusing on trading implications
- Include specific numbers, percentages, or price levels from the news
- Tailor the tone and focus to "{channel_brand}" brand
- Use relevant emojis: 🛢️💰💵💶₿📊📈📉🏦⚡🌍
- Language: {language}
- Be concise but informative (2-3 sentences max in main content)

Remember: This content represents "{channel_brand}" - make it unique and valuable for their specific audience."""
    },
    
    "events": {
        "fa": """You are a professional economic analyst for "{channel_brand}".
Create an ORIGINAL economic event analysis tailored specifically for this channel's trading audience.

Event Information:
Title: {title}
Country: {country}
Currency: {currency}
Previous: {previous}
Forecast: {forecast}
Actual: {actual}
Impact Level: {impact_level}
Language: {language}
Channel Brand: {channel_brand}

IMPORTANT: Create UNIQUE analysis for this specific channel. Do NOT use generic templates.

FORMAT STRUCTURE (create original content following this pattern):

{country_flag} [Write original event title] {relevant_emoji}

[Write 2-3 original sentences analyzing this event's market impact. Include specific data comparison (Previous vs Forecast vs Actual if available). Explain currency implications and trading opportunities. Place ONE relevant emoji in the middle of this analysis.]

🔔همراه ما باشید

📱 @{channel_handle}

ANALYSIS REQUIREMENTS:
- Write ORIGINAL 2-3 sentence analysis (not just 6 words)
- Compare actual vs forecast vs previous values when available
- Explain specific impact on {currency} and related markets
- Discuss trading implications and market sentiment
- Include specific numbers and percentages from the event data
- Tailor analysis tone to "{channel_brand}" audience
- Use relevant emojis: 👥📈🏭🏦🌍🛒📊💰⚡
- Language: {language}

Remember: This analysis represents "{channel_brand}" expertise - make it insightful and valuable for traders."""
    },

    "signal_update": {
        "fa": """Create a trade result post for {tp_level_text} hit:

Signal: {symbol}
Direction: {direction}
Entry: {entry_price}
Exit: {exit_price}
Result: {profit_loss}
Status: {status}
TP Level: {tp_level}
Channel Brand: {channel_brand}

EXACT format for TP hit:
{result_emoji} {tp_level_text} HIT {result_emoji}
➖➖➖➖➖➖➖➖➖➖➖➖➖
COIN: {symbol} 🎯
{direction} | {profit_loss}

📍 ENTRY: {entry_price}
📍 {tp_level_text}: {exit_price}

{break_even_message}

CHANNEL ID @{channel_handle} ✅

EXACT format for SL/Final:
{result_emoji} TRADE RESULT {result_emoji}
➖➖➖➖➖➖➖➖➖➖➖➖➖
COIN: {symbol} 🐃
{direction} | {profit_loss}

📍 ENTRY: {entry_price}
📍 EXIT: {exit_price}

CHANNEL ID @{channel_handle} ✅

RULES:
- Use appropriate format based on status
- For TP1/TP2/TP3 hits, use first format
- For SL_HIT/ALL_TP_HIT/EXPIRED, use second format
- NO greetings or extra text
- Maximum 4 digits for prices
- Language: {language}"""
    },

    "market_analysis": {
        "fa": """Generate a very short market analysis (max 4 lines) for {symbol} {timeframe}.

Current: {current_price} ({change_str})

Format:
📊 {symbol} {timeframe} | {current_price}
📈 [Trend direction in 2-3 words]
🎯 [Key level or target]
💡 [Brief outlook]

Language: {language}
Keep under 200 characters total. NO greetings or explanations."""
    },

    "countdown": {
        "fa": """Create a countdown post with detailed analysis for "{channel_brand}".

Event Information:
Title: {title}
Country: {country}
Currency: {currency}
Previous: {previous}
Forecast: {forecast}
Minutes until: {minutes_until}
Language: {language}
Channel Brand: {channel_brand}

FORMAT STRUCTURE:
⏰ {country_flag} [Event Title] in {minutes_until}min {impact_emoji}

📊 Previous: {previous} | Forecast: {forecast}

[Write 2-3 sentences analyzing the expected market impact of this event. Explain what traders should watch for, potential currency movements, and market implications. Include ONE relevant emoji in the middle.]

📱 @{channel_handle}

ANALYSIS REQUIREMENTS:
- Write 2-3 sentences of meaningful analysis (not just 6 words)
- Explain what to expect from this event based on forecast vs previous
- Discuss potential {currency} impact and market movements
- Mention key levels or scenarios traders should watch
- Tailor analysis to "{channel_brand}" trading audience
- Language: {language}
- Keep it concise but informative

Remember: Provide valuable pre-event analysis that helps traders prepare."""
    },

    "event_result": {
        "fa": """You are a professional economic analyst for "{channel_brand}".
Create an IMMEDIATE REACTION analysis for this just-released economic data.

Event Information:
Title: {title}
Country: {country}
Currency: {currency}
Previous: {previous}
Forecast: {forecast}
ACTUAL: {actual} ⚡ JUST RELEASED
Impact Level: {impact_level}
Language: {language}

FORMAT STRUCTURE:
🚨 {country_flag} [Event Title] RELEASED {impact_emoji}

📊 Actual: {actual} | Forecast: {forecast} | Previous: {previous}

[Write 2-3 sentences analyzing the immediate market impact. Compare actual vs forecast. Explain what this means for {currency} and trading opportunities.]

⚡ Live Analysis
📱 @{channel_handle}

REQUIREMENTS:
- Focus on IMMEDIATE market reaction
- Compare actual vs forecast vs previous
- Explain currency impact and trading implications
- Keep under 200 characters total
- Language: {language}"""
    }
}

async def create_prompt_templates_table():
    """Create the prompt_templates table"""
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS prompt_templates (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        post_type VARCHAR(50) NOT NULL,
        language VARCHAR(10) NOT NULL DEFAULT 'fa',
        template_content TEXT NOT NULL,
        description TEXT,
        active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(post_type, language)
    );
    """
    
    async for db in get_async_db():
        try:
            if is_sqlite_db():
                db.execute(text(create_table_sql))
                db.commit()
            else:
                await db.execute(text(create_table_sql))
                await db.commit()
            logger.info("✅ Created prompt_templates table")
        except Exception as e:
            logger.error(f"❌ Error creating prompt_templates table: {e}")
            raise

async def populate_default_prompts():
    """Populate the table with default prompts"""
    async for db in get_async_db():
        try:
            for post_type, languages in DEFAULT_PROMPTS.items():
                for language, template_content in languages.items():
                    # Check if prompt already exists
                    check_sql = "SELECT id FROM prompt_templates WHERE post_type = :post_type AND language = :language"

                    if is_sqlite_db():
                        result = db.execute(text(check_sql), {"post_type": post_type, "language": language})
                        existing = result.fetchone()
                    else:
                        result = await db.execute(text(check_sql), {"post_type": post_type, "language": language})
                        existing = result.fetchone()

                    if not existing:
                        insert_sql = """
                        INSERT INTO prompt_templates (post_type, language, template_content, description, active)
                        VALUES (:post_type, :language, :template_content, :description, 1)
                        """
                        description = f"Default {language.upper()} template for {post_type} posts"

                        params = {
                            "post_type": post_type,
                            "language": language,
                            "template_content": template_content,
                            "description": description
                        }

                        if is_sqlite_db():
                            db.execute(text(insert_sql), params)
                        else:
                            await db.execute(text(insert_sql), params)

                        logger.info(f"✅ Added default prompt for {post_type} ({language})")

            if is_sqlite_db():
                db.commit()
            else:
                await db.commit()

            logger.info("✅ Populated default prompts")
        except Exception as e:
            logger.error(f"❌ Error populating default prompts: {e}")
            raise

async def run_migration():
    """Run the complete migration"""
    logger.info("🚀 Starting prompt templates migration...")
    
    try:
        await create_prompt_templates_table()
        await populate_default_prompts()
        logger.info("✅ Prompt templates migration completed successfully")
    except Exception as e:
        logger.error(f"❌ Migration failed: {e}")
        raise

if __name__ == "__main__":
    import asyncio
    asyncio.run(run_migration())
