"""
Chart generation module for MignalyBot
"""

import os
import logging
import asyncio
import warnings
from datetime import datetime, timezone
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import pandas as pd
import numpy as np
import pytz

# Suppress font warnings for Unicode characters
warnings.filterwarnings("ignore", category=UserWarning, module="matplotlib")
from sqlalchemy import select

from src.database.setup import get_async_db
from src.database.models import CandleData, SignalStatus

logger = logging.getLogger(__name__)

# Dark theme configuration for beautiful charts
DARK_THEME = {
    'background': '#0d1421',  # Dark blue background
    'grid': '#1e2a3a',        # Subtle grid lines
    'text': '#ffffff',        # White text
    'up_candle': '#00ff88',   # Bright green for bullish candles
    'down_candle': '#ff4757', # Bright red for bearish candles
    'up_volume': '#00ff8844', # Semi-transparent green for volume
    'down_volume': '#ff475744', # Semi-transparent red for volume
    'ema_fast': '#00d2ff',    # Bright cyan for fast EMA
    'ema_slow': '#ffa502',    # Orange for slow EMA
    'entry_buy': '#00ff88',   # Green for buy entry
    'entry_sell': '#ff4757',  # Red for sell entry
    'take_profit': '#00ff88', # Green for TP
    'stop_loss': '#ff4757',   # Red for SL
    'entry_line': '#00d2ff',  # Cyan for entry line
    'signal_bg': '#1a2332'    # Slightly lighter background for signal areas
}

def setup_dark_theme():
    """Setup matplotlib with dark theme styling"""
    plt.style.use('dark_background')
    plt.rcParams.update({
        'figure.facecolor': DARK_THEME['background'],
        'axes.facecolor': DARK_THEME['background'],
        'axes.edgecolor': DARK_THEME['grid'],
        'axes.labelcolor': DARK_THEME['text'],
        'text.color': DARK_THEME['text'],
        'xtick.color': DARK_THEME['text'],
        'ytick.color': DARK_THEME['text'],
        'grid.color': DARK_THEME['grid'],
        'grid.alpha': 0.3,
        'axes.grid': True,
        'font.size': 10,
        'axes.titlesize': 14,
        'axes.labelsize': 12,
        'xtick.labelsize': 10,
        'ytick.labelsize': 10,
        'legend.fontsize': 10,
        'figure.titlesize': 16
    })

def ensure_timezone_aware(dt):
    """
    Ensure a datetime object is timezone-aware

    Args:
        dt: datetime object or pandas timestamp

    Returns:
        timezone-aware datetime object
    """
    if dt is None:
        return None

    try:
        # Handle pandas Timestamp
        if hasattr(dt, 'tz'):
            if dt.tz is None:
                # Assume UTC if naive
                return dt.tz_localize('UTC')
            return dt

        # Handle regular datetime
        if isinstance(dt, datetime):
            if dt.tzinfo is None:
                # Assume UTC if naive
                return pytz.UTC.localize(dt)
            return dt

        # Try to convert to datetime if it's a string or other type
        if isinstance(dt, str):
            dt = pd.to_datetime(dt)
            if hasattr(dt, 'tz') and dt.tz is None:
                return dt.tz_localize('UTC')
            elif isinstance(dt, datetime) and dt.tzinfo is None:
                return pytz.UTC.localize(dt)
            return dt

    except Exception as e:
        logger.warning(f"Error ensuring timezone awareness for {dt}: {e}")
        # Return the original object if we can't process it
        pass

    return dt

async def generate_analysis_chart(symbol, timeframe, candles, save_path=None):
    """
    Generate a chart for market analysis

    Args:
        symbol (str): Trading symbol
        timeframe (str): Timeframe for the chart
        candles (list): List of candle data
        save_path (str, optional): Path to save the chart image

    Returns:
        str: Path to the saved chart image, or None if generation failed
    """
    try:
        # Create images directory if it doesn't exist
        os.makedirs("images", exist_ok=True)

        # Generate a filename if not provided
        if not save_path:
            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S")
            save_path = f"images/analysis_{symbol.replace('/', '')}_{timeframe}_{timestamp}.png"

        if not candles:
            logger.warning(f"No candle data provided for {symbol} {timeframe}")
            return None

        # Convert to pandas DataFrame
        df = pd.DataFrame([
            {
                "timestamp": candle.timestamp,
                "open": candle.open,
                "high": candle.high,
                "low": candle.low,
                "close": candle.close,
                "volume": candle.volume if candle.volume else 0
            }
            for candle in candles
        ])

        # Sort by timestamp
        df = df.sort_values("timestamp")

        # Filter out null/NaN values that can occur during weekends for crypto/forex
        # This prevents plotting empty time periods without data
        df = df.dropna(subset=['open', 'high', 'low', 'close'])

        if df.empty:
            logger.warning(f"No valid candle data after filtering null values for {symbol} {timeframe}")
            return None

        # Setup dark theme
        setup_dark_theme()

        # Generate chart with dark theme
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10), gridspec_kw={'height_ratios': [3, 1]})
        fig.patch.set_facecolor(DARK_THEME['background'])

        # Plot candlesticks
        plot_candlesticks(ax1, df)

        # Plot volume
        plot_volume(ax2, df)

        # Add indicators for analysis
        add_indicators(ax1, df)

        # Fix y-axis scaling for price chart
        fix_price_axis_scaling(ax1, df)

        # Set title and labels with dark theme
        ax1.set_title(f"{symbol} {timeframe} Market Analysis", fontsize=16, fontweight='bold', color=DARK_THEME['text'])
        ax1.set_ylabel("Price", fontsize=14, color=DARK_THEME['text'])
        ax2.set_ylabel("Volume", fontsize=14, color=DARK_THEME['text'])
        ax2.set_xlabel("Time", fontsize=14, color=DARK_THEME['text'])

        # Format x-axis
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))

        # Rotate x-axis labels and set color
        plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45, color=DARK_THEME['text'])
        plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45, color=DARK_THEME['text'])

        # Add grid with dark theme
        ax1.grid(True, alpha=0.4, color=DARK_THEME['grid'])
        ax2.grid(True, alpha=0.4, color=DARK_THEME['grid'])

        # Adjust layout
        plt.tight_layout()

        # Save chart with dark background
        plt.savefig(save_path, dpi=150, bbox_inches="tight", facecolor=DARK_THEME['background'])
        plt.close(fig)

        logger.info(f"Analysis chart generated and saved to {save_path}")
        return save_path

    except Exception as e:
        logger.error(f"Error generating analysis chart: {e}", exc_info=True)
        return None

async def generate_signal_chart(signal, save_path=None, chart_type="signal"):
    """
    Generate a chart for a trading signal

    Args:
        signal (TradingSignal): Trading signal to generate chart for
        save_path (str, optional): Path to save the chart image
        chart_type (str): Type of chart - "signal" for new signals, "performance" for results

    Returns:
        str: Path to the saved chart image, or None if generation failed
    """
    try:
        # Create images directory if it doesn't exist
        os.makedirs("images", exist_ok=True)

        # Generate a filename if not provided
        if not save_path:
            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S")
            save_path = f"images/{chart_type}_{signal.id}_{timestamp}.png"

        # For performance charts, always get fresh data to show current market state
        if chart_type == "performance":
            logger.info(f"Performance chart requested - fetching fresh market data for {signal.symbol}")
            candles = await get_fresh_candle_data_for_chart(signal.symbol, signal.timeframe, 100)
        else:
            # For regular signal charts, use existing logic
            candles = await get_candle_data(signal.symbol, signal.timeframe, 100)

        if not candles:
            logger.warning(f"No candle data found for {signal.symbol} {signal.timeframe}")
            return None

        # Convert to pandas DataFrame
        df = pd.DataFrame([
            {
                "timestamp": candle.timestamp,
                "open": candle.open,
                "high": candle.high,
                "low": candle.low,
                "close": candle.close,
                "volume": candle.volume if candle.volume else 0
            }
            for candle in candles
        ])

        # Sort by timestamp
        df = df.sort_values("timestamp")

        # Filter out null/NaN values that can occur during weekends for crypto/forex
        # This prevents plotting empty time periods without data
        df = df.dropna(subset=['open', 'high', 'low', 'close'])

        if df.empty:
            logger.warning(f"No valid candle data after filtering null values for {signal.symbol} {signal.timeframe}")
            return None

        # Setup dark theme
        setup_dark_theme()

        # Generate chart with dark theme
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10), gridspec_kw={'height_ratios': [3, 1]})
        fig.patch.set_facecolor(DARK_THEME['background'])

        # Plot candlesticks
        plot_candlesticks(ax1, df)

        # Plot volume
        plot_volume(ax2, df)

        # Add signal markers
        add_signal_markers(ax1, signal, df)

        # Add indicators
        add_indicators(ax1, df)

        # Fix y-axis scaling for price chart
        fix_price_axis_scaling(ax1, df, signal)

        # Set title and labels
        direction = "BUY" if signal.direction == "buy" else "SELL"
        if chart_type == "performance":
            # Show result in title for performance charts
            if signal.status == SignalStatus.TP_HIT:
                result_text = "[+] TARGET HIT"
            elif signal.status == SignalStatus.SL_HIT:
                result_text = "[-] STOP LOSS"
            else:
                result_text = "[*] RESULT"
            fig.suptitle(f"{signal.symbol} {signal.timeframe} - {direction} {result_text}",
                        fontsize=18, fontweight='bold', color=DARK_THEME['text'])
        else:
            fig.suptitle(f"{signal.symbol} {signal.timeframe} - {direction} Signal",
                        fontsize=18, fontweight='bold', color=DARK_THEME['text'])
        ax1.set_ylabel("Price", fontsize=14, color=DARK_THEME['text'])
        ax2.set_ylabel("Volume", fontsize=14, color=DARK_THEME['text'])
        ax2.set_xlabel("Date", fontsize=14, color=DARK_THEME['text'])

        # Format x-axis dates
        format_dates(ax1, ax2)

        # Add grid with dark theme
        ax1.grid(True, alpha=0.4, color=DARK_THEME['grid'])
        ax2.grid(True, alpha=0.4, color=DARK_THEME['grid'])

        # Adjust layout
        plt.tight_layout()

        # Save chart with dark background
        plt.savefig(save_path, dpi=150, bbox_inches="tight", facecolor=DARK_THEME['background'])
        plt.close(fig)

        logger.info(f"Chart generated and saved to {save_path}")
        return save_path

    except Exception as e:
        logger.error(f"Error generating chart: {e}", exc_info=True)
        return None

async def get_fresh_candle_data_for_chart(symbol, timeframe, limit=100):
    """
    Get fresh candle data directly from MT5 API for performance charts
    This ensures we have the most current market data including recent TP/SL hits

    Args:
        symbol (str): Symbol to get data for
        timeframe (str): Timeframe to get data for
        limit (int, optional): Maximum number of candles to retrieve

    Returns:
        list: List of candle data dictionaries with fresh market data
    """
    from src.data_collection.market_data import get_mt5_candle_data

    try:
        # Convert symbol format for MT5 API (e.g., "BTC/USD" -> "BTCUSD")
        mt5_symbol = symbol.replace("/", "").replace("-", "")

        logger.info(f"Fetching fresh candle data for chart: {mt5_symbol} {timeframe} (limit: {limit})")

        # Get fresh data directly from MT5 API
        fresh_candles = await get_mt5_candle_data(mt5_symbol, timeframe, limit)

        if fresh_candles and len(fresh_candles) > 0:
            logger.info(f"Retrieved {len(fresh_candles)} fresh candles for chart generation")

            # Convert to CandleData-like objects for compatibility with chart generation
            class ChartCandle:
                def __init__(self, data):
                    self.timestamp = data.get("timestamp")
                    self.open = data.get("open")
                    self.high = data.get("high")
                    self.low = data.get("low")
                    self.close = data.get("close")
                    self.volume = data.get("volume", 0)

            return [ChartCandle(candle) for candle in fresh_candles]
        else:
            logger.warning(f"No fresh candle data available from MT5 API for {symbol} {timeframe}")
            # Fallback to database data if MT5 API fails
            return await get_candle_data(symbol, timeframe, limit)

    except Exception as e:
        logger.error(f"Error fetching fresh candle data for chart: {e}", exc_info=True)
        # Fallback to database data if there's an error
        return await get_candle_data(symbol, timeframe, limit)

async def get_candle_data(symbol, timeframe, limit=100):
    """
    Get candle data for a symbol and timeframe with fallback to MT5 API

    Args:
        symbol (str): Symbol to get data for
        timeframe (str): Timeframe to get data for
        limit (int, optional): Maximum number of candles to retrieve

    Returns:
        list: List of CandleData objects
    """
    from src.database.setup import AsyncSessionLocal
    from src.data_collection.market_data import get_mt5_candle_data

    try:
        async for db in get_async_db():
            # Handle both synchronous and asynchronous operations
            if AsyncSessionLocal is None:
                # Synchronous operation for SQLite
                result = db.execute(
                    select(CandleData)
                    .where(
                        CandleData.symbol == symbol,
                        CandleData.timeframe == timeframe
                    )
                    .order_by(CandleData.timestamp.desc())
                    .limit(limit)
                )
            else:
                # Asynchronous operation for other databases
                result = await db.execute(
                    select(CandleData)
                    .where(
                        CandleData.symbol == symbol,
                        CandleData.timeframe == timeframe
                    )
                    .order_by(CandleData.timestamp.desc())
                    .limit(limit)
                )

            candles = result.scalars().all()

            if candles and len(candles) >= 10:  # Need at least 10 candles for chart
                logger.info(f"Found {len(candles)} candles for {symbol} {timeframe} in database")
                return candles
            else:
                # Not enough data in database, try to fetch from MT5
                logger.info(f"Insufficient data in database for {symbol} {timeframe}, fetching from MT5...")
                mt5_symbol = symbol.replace("/", "").replace("-", "")

                # Get fresh data from MT5 API
                fresh_data = await get_mt5_candle_data(mt5_symbol, timeframe, limit)

                if fresh_data:
                    logger.info(f"Retrieved {len(fresh_data)} candles from MT5 for chart generation")
                    # Convert dict format to CandleData-like objects for chart generation
                    candle_objects = []
                    for candle_dict in fresh_data:
                        # Create a simple object with the required attributes
                        class CandleObj:
                            def __init__(self, data):
                                self.timestamp = data["timestamp"]
                                self.open = data["open"]
                                self.high = data["high"]
                                self.low = data["low"]
                                self.close = data["close"]
                                self.volume = data.get("volume", 0)

                        candle_objects.append(CandleObj(candle_dict))

                    return candle_objects
                else:
                    logger.warning(f"No data available for {symbol} {timeframe} from MT5 API")
                    return []

    except Exception as e:
        logger.error(f"Error getting candle data for chart: {e}", exc_info=True)
        return []

def plot_candlesticks(ax, df):
    """
    Plot candlesticks on the given axis

    Args:
        ax (matplotlib.axes.Axes): Axis to plot on
        df (pandas.DataFrame): DataFrame containing candle data
    """
    try:
        if df.empty:
            logger.warning("Empty DataFrame provided for candlestick plotting")
            return

        # Additional check for null values in price data
        if df[['open', 'high', 'low', 'close']].isnull().any().any():
            logger.warning("DataFrame contains null values in price data, filtering...")
            df = df.dropna(subset=['open', 'high', 'low', 'close'])
            if df.empty:
                logger.warning("No valid data remaining after filtering null values")
                return

        # Ensure timestamps are timezone-aware for calculation
        timestamps = df["timestamp"].apply(ensure_timezone_aware)

        # Calculate width of candlesticks safely
        if len(timestamps) > 1:
            try:
                # Ensure both timestamps are timezone-aware before subtraction
                ts1 = ensure_timezone_aware(timestamps.iloc[1])
                ts0 = ensure_timezone_aware(timestamps.iloc[0])
                time_diff = ts1 - ts0
                if hasattr(time_diff, 'total_seconds'):
                    width = 0.6 * time_diff.total_seconds() / 86400
                else:
                    # Fallback for pandas Timedelta
                    width = 0.6 * time_diff.total_seconds() / 86400
            except (TypeError, ValueError) as e:
                logger.warning(f"Error calculating candlestick width: {e}, using default")
                width = 0.6  # Default width if calculation fails
        else:
            width = 0.6  # Default width if only one candle

        # Plot up and down candles separately
        up = df[df["close"] >= df["open"]]
        down = df[df["close"] < df["open"]]

        # Debug logging for price ranges
        price_min = min(df["low"].min(), df["open"].min(), df["close"].min())
        price_max = max(df["high"].max(), df["open"].max(), df["close"].max())
        logger.debug(f"Candlestick data range: {price_min:.5f} to {price_max:.5f} (range: {price_max - price_min:.5f})")
        logger.debug(f"Up candles: {len(up)}, Down candles: {len(down)}, Total: {len(df)}")

        # Plot up candles with dark theme colors
        ax.bar(
            up["timestamp"],
            up["close"] - up["open"],
            width,
            bottom=up["open"],
            color=DARK_THEME['up_candle'],
            alpha=0.9,
            edgecolor=DARK_THEME['up_candle'],
            linewidth=0.5
        )
        ax.bar(
            up["timestamp"],
            up["high"] - up["close"],
            width * 0.15,
            bottom=up["close"],
            color=DARK_THEME['up_candle'],
            alpha=0.9
        )
        ax.bar(
            up["timestamp"],
            up["open"] - up["low"],
            width * 0.15,
            bottom=up["low"],
            color=DARK_THEME['up_candle'],
            alpha=0.9
        )

        # Plot down candles with dark theme colors
        ax.bar(
            down["timestamp"],
            down["open"] - down["close"],
            width,
            bottom=down["close"],
            color=DARK_THEME['down_candle'],
            alpha=0.9,
            edgecolor=DARK_THEME['down_candle'],
            linewidth=0.5
        )
        ax.bar(
            down["timestamp"],
            down["high"] - down["open"],
            width * 0.15,
            bottom=down["open"],
            color=DARK_THEME['down_candle'],
            alpha=0.9
        )
        ax.bar(
            down["timestamp"],
            down["close"] - down["low"],
            width * 0.15,
            bottom=down["low"],
            color=DARK_THEME['down_candle'],
            alpha=0.9
        )

    except Exception as e:
        logger.error(f"Error plotting candlesticks: {e}", exc_info=True)
        # Continue without detailed candlesticks if there's an error

def plot_volume(ax, df):
    """
    Plot volume on the given axis

    Args:
        ax (matplotlib.axes.Axes): Axis to plot on
        df (pandas.DataFrame): DataFrame containing candle data
    """
    try:
        # Check for empty DataFrame or null values
        if df.empty:
            logger.warning("Empty DataFrame provided for volume plotting")
            return

        # Filter out rows with null volume or price data
        df_clean = df.dropna(subset=['volume', 'open', 'close'])
        if df_clean.empty:
            logger.warning("No valid data for volume plotting after filtering null values")
            return

        # Use cleaned DataFrame for plotting
        df = df_clean

        # Ensure timestamps are timezone-aware for calculation
        timestamps = df["timestamp"].apply(ensure_timezone_aware)

        # Calculate width of volume bars safely
        if len(timestamps) > 1:
            try:
                # Ensure both timestamps are timezone-aware before subtraction
                ts1 = ensure_timezone_aware(timestamps.iloc[1])
                ts0 = ensure_timezone_aware(timestamps.iloc[0])
                time_diff = ts1 - ts0
                if hasattr(time_diff, 'total_seconds'):
                    width = 0.6 * time_diff.total_seconds() / 86400
                else:
                    # Fallback for pandas Timedelta
                    width = 0.6 * time_diff.total_seconds() / 86400
            except (TypeError, ValueError) as e:
                logger.warning(f"Error calculating volume bar width: {e}, using default")
                width = 0.6  # Default width if calculation fails
        else:
            width = 0.6  # Default width if only one candle

        # Plot up and down volume separately
        up = df[df["close"] >= df["open"]]
        down = df[df["close"] < df["open"]]

        # Plot up volume with dark theme colors
        ax.bar(
            up["timestamp"],
            up["volume"],
            width,
            color=DARK_THEME['up_volume'],
            alpha=0.7
        )

        # Plot down volume with dark theme colors
        ax.bar(
            down["timestamp"],
            down["volume"],
            width,
            color=DARK_THEME['down_volume'],
            alpha=0.7
        )

    except Exception as e:
        logger.error(f"Error plotting volume: {e}", exc_info=True)
        # Continue without volume bars if there's an error

def add_signal_markers(ax, signal, df):
    """
    Add signal markers to the chart

    Args:
        ax (matplotlib.axes.Axes): Axis to plot on
        signal (TradingSignal): Trading signal to add markers for
        df (pandas.DataFrame): DataFrame containing candle data
    """
    try:
        # Ensure both timestamps are timezone-aware for comparison
        entry_time = ensure_timezone_aware(signal.entry_time)

        # Ensure DataFrame timestamps are timezone-aware
        df_timestamps = df["timestamp"].apply(ensure_timezone_aware)

        # Find the entry candle (closest to entry time)
        time_diffs = df_timestamps.sub(entry_time).abs()
        entry_candle_idx = time_diffs.idxmin()
        entry_candle = df.iloc[entry_candle_idx]

        # Add entry marker with dark theme colors
        if signal.direction == "buy":
            ax.scatter(
                entry_candle["timestamp"],
                signal.entry_price * 0.99,  # Slightly below for visibility
                marker="^",
                color=DARK_THEME['entry_buy'],
                s=200,
                label="Entry",
                edgecolors='white',
                linewidth=2,
                zorder=5
            )
        else:
            ax.scatter(
                entry_candle["timestamp"],
                signal.entry_price * 1.01,  # Slightly above for visibility
                marker="v",
                color=DARK_THEME['entry_sell'],
                s=200,
                label="Entry",
                edgecolors='white',
                linewidth=2,
                zorder=5
            )

        # Add horizontal lines for entry, stop loss, and take profit with dark theme colors
        ax.axhline(y=signal.entry_price, color=DARK_THEME['entry_line'], linestyle="--", alpha=0.8, label="Entry Price", linewidth=2)
        ax.axhline(y=signal.stop_loss, color=DARK_THEME['stop_loss'], linestyle="--", alpha=0.8, label="Stop Loss", linewidth=2)
        ax.axhline(y=signal.take_profit, color=DARK_THEME['take_profit'], linestyle="--", alpha=0.8, label="Take Profit", linewidth=2)

        # Add exit marker if signal is closed
        if signal.exit_time and signal.exit_price:
            # Ensure exit time is timezone-aware
            exit_time = ensure_timezone_aware(signal.exit_time)

            # Find the exit candle (closest to exit time)
            exit_time_diffs = df_timestamps.sub(exit_time).abs()
            exit_candle_idx = exit_time_diffs.idxmin()
            exit_candle = df.iloc[exit_candle_idx]

            # Add exit marker with dark theme colors
            if signal.status.value == "tp_hit":
                ax.scatter(
                    exit_candle["timestamp"],
                    signal.exit_price,
                    marker="*",
                    color=DARK_THEME['take_profit'],
                    s=300,
                    label="Take Profit Hit",
                    edgecolors='white',
                    linewidth=2,
                    zorder=5
                )
            elif signal.status.value == "sl_hit":
                ax.scatter(
                    exit_candle["timestamp"],
                    signal.exit_price,
                    marker="X",
                    color=DARK_THEME['stop_loss'],
                    s=300,
                    label="Stop Loss Hit",
                    edgecolors='white',
                    linewidth=2,
                    zorder=5
                )
            else:
                ax.scatter(
                    exit_candle["timestamp"],
                    signal.exit_price,
                    marker="o",
                    color=DARK_THEME['text'],
                    s=200,
                    label="Exit",
                    edgecolors='white',
                    linewidth=2,
                    zorder=5
                )

        # Add legend
        ax.legend(loc="upper left")

    except Exception as e:
        logger.error(f"Error adding signal markers: {e}", exc_info=True)
        # Continue without markers if there's an error

def add_indicators(ax, df):
    """
    Add technical indicators to the chart

    Args:
        ax (matplotlib.axes.Axes): Axis to plot on
        df (pandas.DataFrame): DataFrame containing candle data
    """
    # Calculate EMAs
    df["ema9"] = df["close"].ewm(span=9, adjust=False).mean()
    df["ema21"] = df["close"].ewm(span=21, adjust=False).mean()

    # Plot EMAs with dark theme colors
    ax.plot(df["timestamp"], df["ema9"], color=DARK_THEME['ema_fast'], linewidth=2.5, label="EMA 9", alpha=0.9)
    ax.plot(df["timestamp"], df["ema21"], color=DARK_THEME['ema_slow'], linewidth=2.5, label="EMA 21", alpha=0.9)

def format_dates(ax1, ax2):
    """
    Format the date axis

    Args:
        ax1 (matplotlib.axes.Axes): Price axis
        ax2 (matplotlib.axes.Axes): Volume axis
    """
    # Format x-axis dates on both axes
    date_format = mdates.DateFormatter("%m-%d %H:%M")
    ax1.xaxis.set_major_formatter(date_format)
    ax2.xaxis.set_major_formatter(date_format)

    # Set x-axis tick parameters
    ax1.tick_params(axis="x", rotation=45)
    ax2.tick_params(axis="x", rotation=45)

    # Hide x-axis labels on the top plot
    ax1.tick_params(axis="x", labelbottom=False)

def fix_price_axis_scaling(ax, df, signal=None):
    """
    Fix y-axis scaling for price charts to prevent squished candlesticks

    Args:
        ax (matplotlib.axes.Axes): Price axis to fix
        df (pandas.DataFrame): DataFrame containing candle data
        signal (TradingSignal, optional): Trading signal for additional context
    """
    try:
        if df.empty:
            return

        # Get price range from the data
        price_min = min(df["low"].min(), df["open"].min(), df["close"].min())
        price_max = max(df["high"].max(), df["open"].max(), df["close"].max())

        # Include signal levels if provided
        if signal:
            signal_prices = [signal.entry_price, signal.stop_loss, signal.take_profit]
            signal_prices = [p for p in signal_prices if p is not None]
            if signal_prices:
                price_min = min(price_min, min(signal_prices))
                price_max = max(price_max, max(signal_prices))

        # Calculate price range and add padding
        price_range = price_max - price_min

        # Add 5% padding on each side for better visualization
        padding = price_range * 0.05
        y_min = price_min - padding
        y_max = price_max + padding

        # Ensure we have a minimum range for very stable prices
        if price_range < 0.0001:  # For very small ranges (like some forex pairs)
            center = (price_min + price_max) / 2
            y_min = center - 0.0005
            y_max = center + 0.0005

        # Set the y-axis limits
        ax.set_ylim(y_min, y_max)

        # Format y-axis to show appropriate decimal places
        if price_max < 10:  # Forex pairs typically
            ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:.5f}'))
        elif price_max < 1000:  # Some indices or commodities
            ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:.3f}'))
        else:  # Crypto or high-value assets
            ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:.2f}'))

        logger.debug(f"Fixed y-axis scaling: {y_min:.5f} to {y_max:.5f} (range: {price_range:.5f})")

    except Exception as e:
        logger.error(f"Error fixing price axis scaling: {e}", exc_info=True)
