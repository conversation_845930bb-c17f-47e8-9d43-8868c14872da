"""
Optimized database connection manager for MignalyBot v2

Features:
- Connection pooling with configurable settings
- Automatic connection health checks
- Query optimization and caching
- Batch operations support
- Performance monitoring
- Graceful error handling and recovery
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Optional, Dict, Any, List
from sqlalchemy import create_engine, text, event
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool, NullPool
from sqlalchemy.exc import SQLAlchemyError, DisconnectionError

from core.config.settings import Settings
from core.exceptions.base import DatabaseException, handle_exception
from core.utils.helpers import performance_monitor, async_performance_monitor


class DatabaseManager:
    """Optimized database connection manager"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        
        # Connection objects
        self.async_engine = None
        self.sync_engine = None
        self.async_session_factory = None
        self.sync_session_factory = None
        
        # Performance tracking
        self.query_cache: Dict[str, Any] = {}
        self.connection_stats = {
            'total_queries': 0,
            'failed_queries': 0,
            'avg_query_time': 0.0,
            'active_connections': 0
        }
    
    async def initialize(self):
        """Initialize database connections and engines"""
        try:
            self.logger.info("Initializing database manager")
            
            # Create engines based on database type
            if self.settings.database.is_sqlite:
                await self._initialize_sqlite()
            else:
                await self._initialize_async_db()
            
            # Setup event listeners for performance monitoring
            self._setup_event_listeners()
            
            # Test connections
            await self._test_connections()
            
            self.logger.info("Database manager initialized successfully")
            
        except Exception as e:
            handle_exception(
                e, self.logger, "database_initialization", reraise=True
            )
    
    async def _initialize_sqlite(self):
        """Initialize SQLite connections"""
        # SQLite with optimized settings
        self.sync_engine = create_engine(
            self.settings.database.url,
            connect_args={
                "check_same_thread": False,
                "timeout": 30,
                "isolation_level": None  # Autocommit mode
            },
            poolclass=NullPool,  # No pooling for SQLite
            echo=self.settings.database.echo
        )
        
        self.sync_session_factory = sessionmaker(
            bind=self.sync_engine,
            autocommit=False,
            autoflush=False
        )
        
        # For SQLite, we'll use sync operations wrapped in async
        self.async_session_factory = self.sync_session_factory
    
    async def _initialize_async_db(self):
        """Initialize async database connections (PostgreSQL, MySQL, etc.)"""
        # Async engine with connection pooling
        self.async_engine = create_async_engine(
            self.settings.database.async_url,
            pool_size=self.settings.database.pool_size,
            max_overflow=self.settings.database.max_overflow,
            pool_timeout=self.settings.database.pool_timeout,
            pool_recycle=self.settings.database.pool_recycle,
            pool_pre_ping=self.settings.database.pool_pre_ping,
            echo=self.settings.database.echo,
            poolclass=QueuePool
        )
        
        self.async_session_factory = async_sessionmaker(
            bind=self.async_engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
        
        # Sync engine for migrations and admin operations
        sync_url = self.settings.database.url
        self.sync_engine = create_engine(
            sync_url,
            pool_size=5,
            max_overflow=10,
            pool_timeout=30,
            pool_recycle=300,
            pool_pre_ping=True,
            echo=self.settings.database.echo
        )
        
        self.sync_session_factory = sessionmaker(
            bind=self.sync_engine,
            autocommit=False,
            autoflush=False
        )
    
    def _setup_event_listeners(self):
        """Setup SQLAlchemy event listeners for monitoring"""
        if self.async_engine:
            @event.listens_for(self.async_engine.sync_engine, "before_cursor_execute")
            def before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
                context._query_start_time = asyncio.get_event_loop().time()
            
            @event.listens_for(self.async_engine.sync_engine, "after_cursor_execute")
            def after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
                total_time = asyncio.get_event_loop().time() - context._query_start_time
                self._update_query_stats(total_time, success=True)
        
        if self.sync_engine:
            @event.listens_for(self.sync_engine, "before_cursor_execute")
            def before_cursor_execute_sync(conn, cursor, statement, parameters, context, executemany):
                import time
                context._query_start_time = time.time()
            
            @event.listens_for(self.sync_engine, "after_cursor_execute")
            def after_cursor_execute_sync(conn, cursor, statement, parameters, context, executemany):
                import time
                total_time = time.time() - context._query_start_time
                self._update_query_stats(total_time, success=True)
    
    def _update_query_stats(self, query_time: float, success: bool = True):
        """Update query performance statistics"""
        self.connection_stats['total_queries'] += 1
        
        if not success:
            self.connection_stats['failed_queries'] += 1
        
        # Update average query time
        total_queries = self.connection_stats['total_queries']
        current_avg = self.connection_stats['avg_query_time']
        self.connection_stats['avg_query_time'] = (
            (current_avg * (total_queries - 1) + query_time) / total_queries
        )
    
    async def _test_connections(self):
        """Test database connections"""
        try:
            if self.settings.database.is_sqlite:
                # Test sync connection for SQLite
                async with self.get_sync_session() as session:
                    session.execute(text("SELECT 1"))
            else:
                # Test async connection
                async with self.get_async_session() as session:
                    await session.execute(text("SELECT 1"))
            
            self.logger.info("Database connection test successful")
            
        except Exception as e:
            raise DatabaseException(
                "Database connection test failed",
                context={'database_url': self.settings.database.url},
                original_exception=e
            )
    
    @asynccontextmanager
    async def get_async_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get async database session with automatic cleanup"""
        if self.settings.database.is_sqlite:
            # For SQLite, use sync session wrapped in async context
            session = self.sync_session_factory()
            try:
                yield session
                session.commit()
            except Exception as e:
                session.rollback()
                self._update_query_stats(0, success=False)
                raise DatabaseException(
                    "Database session error",
                    operation="session_management",
                    original_exception=e
                )
            finally:
                session.close()
        else:
            # True async session
            async with self.async_session_factory() as session:
                try:
                    yield session
                    await session.commit()
                except Exception as e:
                    await session.rollback()
                    self._update_query_stats(0, success=False)
                    raise DatabaseException(
                        "Database session error",
                        operation="session_management",
                        original_exception=e
                    )
    
    @asynccontextmanager
    async def get_sync_session(self) -> Session:
        """Get sync database session with automatic cleanup"""
        session = self.sync_session_factory()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            self._update_query_stats(0, success=False)
            raise DatabaseException(
                "Database session error",
                operation="sync_session_management",
                original_exception=e
            )
        finally:
            session.close()
    
    @async_performance_monitor("batch_execute")
    async def execute_batch(
        self,
        statements: List[str],
        parameters: Optional[List[Dict[str, Any]]] = None
    ) -> List[Any]:
        """
        Execute multiple statements in a batch for better performance
        
        Args:
            statements: List of SQL statements
            parameters: List of parameter dictionaries for each statement
            
        Returns:
            List of results
        """
        results = []
        
        async with self.get_async_session() as session:
            try:
                for i, statement in enumerate(statements):
                    params = parameters[i] if parameters and i < len(parameters) else {}
                    result = await session.execute(text(statement), params)
                    results.append(result)
                
                return results
                
            except Exception as e:
                raise DatabaseException(
                    "Batch execution failed",
                    operation="batch_execute",
                    context={'statements_count': len(statements)},
                    original_exception=e
                )
    
    async def get_connection_stats(self) -> Dict[str, Any]:
        """Get database connection statistics"""
        stats = self.connection_stats.copy()
        
        # Add pool statistics if available
        if self.async_engine and hasattr(self.async_engine.pool, 'size'):
            stats.update({
                'pool_size': self.async_engine.pool.size(),
                'checked_in': self.async_engine.pool.checkedin(),
                'checked_out': self.async_engine.pool.checkedout(),
                'overflow': self.async_engine.pool.overflow(),
                'invalid': self.async_engine.pool.invalid()
            })
        
        return stats
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform database health check"""
        try:
            start_time = asyncio.get_event_loop().time()
            
            # Test connection
            async with self.get_async_session() as session:
                await session.execute(text("SELECT 1"))
            
            response_time = (asyncio.get_event_loop().time() - start_time) * 1000
            
            return {
                'status': 'healthy',
                'response_time_ms': response_time,
                'connection_stats': await self.get_connection_stats()
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e),
                'connection_stats': await self.get_connection_stats()
            }
    
    async def close(self):
        """Close all database connections"""
        try:
            self.logger.info("Closing database connections")
            
            if self.async_engine:
                await self.async_engine.dispose()
            
            if self.sync_engine:
                self.sync_engine.dispose()
            
            self.logger.info("Database connections closed")
            
        except Exception as e:
            self.logger.error(f"Error closing database connections: {e}")


# Global database manager instance
_db_manager: Optional[DatabaseManager] = None


def get_db_manager() -> DatabaseManager:
    """Get global database manager instance"""
    global _db_manager
    if _db_manager is None:
        raise DatabaseException("Database manager not initialized")
    return _db_manager


def set_db_manager(manager: DatabaseManager):
    """Set global database manager instance"""
    global _db_manager
    _db_manager = manager
