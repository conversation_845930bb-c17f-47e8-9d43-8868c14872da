"""
Health monitoring utilities for MignalyBot
Provides network health checks and system monitoring
"""

import asyncio
import logging
import time
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field

from src.utils.network_utils import test_network_connectivity, create_http_client

logger = logging.getLogger(__name__)

@dataclass
class HealthStatus:
    """Health status information"""
    is_healthy: bool = True
    last_check: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    network_status: Dict[str, bool] = field(default_factory=dict)
    response_times: Dict[str, float] = field(default_factory=dict)

class HealthMonitor:
    """System health monitoring"""
    
    def __init__(self):
        self.last_health_check = None
        self.health_check_interval = 300  # 5 minutes
        self.network_endpoints = [
            "https://www.google.com",
            "https://api.telegram.org",
            "https://nfs.faireconomy.media/ff_calendar_thisweek.json",
            "https://www.forexfactory.com"
        ]
        
    async def check_network_health(self) -> Dict[str, Any]:
        """
        Check network connectivity and response times
        
        Returns:
            Dictionary with network health information
        """
        logger.debug("🔍 Checking network health...")
        
        network_status = {}
        response_times = {}
        
        async with create_http_client() as client:
            for endpoint in self.network_endpoints:
                try:
                    start_time = time.time()
                    response = await client.get(endpoint, timeout=10.0)
                    end_time = time.time()
                    
                    response_time = (end_time - start_time) * 1000  # Convert to milliseconds
                    is_healthy = response.status_code < 400
                    
                    network_status[endpoint] = is_healthy
                    response_times[endpoint] = response_time
                    
                    if is_healthy:
                        logger.debug(f"✅ {endpoint}: {response.status_code} ({response_time:.0f}ms)")
                    else:
                        logger.warning(f"⚠️ {endpoint}: {response.status_code} ({response_time:.0f}ms)")
                        
                except Exception as e:
                    network_status[endpoint] = False
                    response_times[endpoint] = -1
                    logger.warning(f"❌ {endpoint}: {e}")
        
        return {
            'network_status': network_status,
            'response_times': response_times,
            'healthy_endpoints': sum(1 for status in network_status.values() if status),
            'total_endpoints': len(network_status),
            'avg_response_time': sum(t for t in response_times.values() if t > 0) / max(1, sum(1 for t in response_times.values() if t > 0))
        }
    
    async def check_system_health(self) -> HealthStatus:
        """
        Perform comprehensive system health check
        
        Returns:
            HealthStatus object with current system status
        """
        logger.debug("🏥 Performing system health check...")
        
        health_status = HealthStatus()
        
        try:
            # Check network health
            network_health = await self.check_network_health()
            health_status.network_status = network_health['network_status']
            health_status.response_times = network_health['response_times']
            
            # Evaluate network health
            healthy_endpoints = network_health['healthy_endpoints']
            total_endpoints = network_health['total_endpoints']
            
            if healthy_endpoints == 0:
                health_status.is_healthy = False
                health_status.errors.append("No network endpoints are reachable")
                logger.error("❌ No network connectivity detected")
            elif healthy_endpoints < total_endpoints / 2:
                health_status.warnings.append(f"Only {healthy_endpoints}/{total_endpoints} endpoints are reachable")
                logger.warning(f"⚠️ Limited network connectivity: {healthy_endpoints}/{total_endpoints} endpoints reachable")
            else:
                logger.debug(f"✅ Network health good: {healthy_endpoints}/{total_endpoints} endpoints reachable")
            
            # Check response times
            avg_response_time = network_health['avg_response_time']
            if avg_response_time > 5000:  # 5 seconds
                health_status.warnings.append(f"High network latency: {avg_response_time:.0f}ms average")
                logger.warning(f"⚠️ High network latency detected: {avg_response_time:.0f}ms")
            elif avg_response_time > 2000:  # 2 seconds
                health_status.warnings.append(f"Elevated network latency: {avg_response_time:.0f}ms average")
                logger.debug(f"⚠️ Elevated network latency: {avg_response_time:.0f}ms")
            
        except Exception as e:
            health_status.is_healthy = False
            health_status.errors.append(f"Health check failed: {e}")
            logger.error(f"❌ Health check failed: {e}")
        
        health_status.last_check = datetime.now(timezone.utc)
        self.last_health_check = health_status.last_check
        
        return health_status
    
    async def should_perform_operation(self, operation_name: str = "operation") -> bool:
        """
        Check if it's safe to perform a network operation
        
        Args:
            operation_name: Name of the operation for logging
            
        Returns:
            True if operation should proceed, False otherwise
        """
        # Perform health check if needed
        if (self.last_health_check is None or 
            (datetime.now(timezone.utc) - self.last_health_check).total_seconds() > self.health_check_interval):
            
            health_status = await self.check_system_health()
            
            if not health_status.is_healthy:
                logger.warning(f"⚠️ Skipping {operation_name} due to poor system health")
                for error in health_status.errors:
                    logger.warning(f"   - {error}")
                return False
            
            if health_status.warnings:
                logger.info(f"⚠️ Proceeding with {operation_name} despite warnings:")
                for warning in health_status.warnings:
                    logger.info(f"   - {warning}")
        
        return True
    
    def log_health_summary(self, health_status: HealthStatus):
        """
        Log a summary of the health status
        
        Args:
            health_status: HealthStatus object to summarize
        """
        if health_status.is_healthy:
            logger.info("✅ System health: GOOD")
        else:
            logger.warning("❌ System health: POOR")
        
        # Log network status
        healthy_count = sum(1 for status in health_status.network_status.values() if status)
        total_count = len(health_status.network_status)
        logger.info(f"🌐 Network: {healthy_count}/{total_count} endpoints reachable")
        
        # Log response times
        if health_status.response_times:
            avg_time = sum(t for t in health_status.response_times.values() if t > 0) / max(1, sum(1 for t in health_status.response_times.values() if t > 0))
            logger.info(f"⏱️ Average response time: {avg_time:.0f}ms")
        
        # Log errors and warnings
        for error in health_status.errors:
            logger.error(f"❌ {error}")
        for warning in health_status.warnings:
            logger.warning(f"⚠️ {warning}")

# Global health monitor instance
health_monitor = HealthMonitor()

async def check_network_before_operation(operation_name: str = "operation") -> bool:
    """
    Convenience function to check network health before an operation
    
    Args:
        operation_name: Name of the operation for logging
        
    Returns:
        True if operation should proceed, False otherwise
    """
    return await health_monitor.should_perform_operation(operation_name)

async def get_system_health() -> HealthStatus:
    """
    Get current system health status
    
    Returns:
        HealthStatus object
    """
    return await health_monitor.check_system_health()
