# MignalyBot v2 Configuration Template
# Copy this file to .env and fill in your values

# =============================================================================
# CORE CONFIGURATION
# =============================================================================

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_WEBHOOK_URL=
TELEGRAM_WEBHOOK_SECRET=
TELEGRAM_MAX_MESSAGE_LENGTH=4096
TELEGRAM_CHUNK_SIZE=3000
TELEGRAM_RATE_LIMIT_MESSAGES=30
TELEGRAM_RATE_LIMIT_WINDOW=60

# AI Configuration (Qwen API)
QWEN_API_KEY=your_qwen_api_key_here
QWEN_ENDPOINT=https://dashscope-intl.aliyuncs.com/compatible-mode/v1
QWEN_MODEL=qwen-max-2025-01-25
AI_MAX_TOKENS=4000
AI_TEMPERATURE=0.7
AI_CACHE_TTL=3600
AI_RATE_LIMIT_REQUESTS=100
AI_RATE_LIMIT_WINDOW=60
AI_TIMEOUT=30
AI_MAX_RETRIES=3
AI_RETRY_DELAY=1.0

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Database URL (SQLite by default, can use PostgreSQL/MySQL)
DATABASE_URL=sqlite:///mignalybot_v2.db
# For PostgreSQL: postgresql://user:password@localhost/mignalybot_v2
# For MySQL: mysql://user:password@localhost/mignalybot_v2

# Database Connection Pool Settings
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=60
DB_POOL_RECYCLE=300
DB_POOL_PRE_PING=true
DB_ECHO=false

# =============================================================================
# DATA COLLECTION CONFIGURATION
# =============================================================================

# Trading Symbols (comma-separated)
SYMBOLS=BTC/USD,ETH/USD,EUR/USD,GBP/USD

# Timeframes (comma-separated)
TIMEFRAMES=1h,4h,1d

# Data Collection Settings
DATA_COLLECTION_WORKERS=5
DATA_COLLECTION_BATCH_SIZE=100
DATA_COLLECTION_TIMEOUT=30
DATA_COLLECTION_RETRY_ATTEMPTS=3
DATA_COLLECTION_RETRY_DELAY=2.0
DATA_COLLECTION_INTERVAL=300

# Feature Flags
ENABLE_NEWS=true
ENABLE_SIGNALS=true
ENABLE_CALENDAR=true

# =============================================================================
# ADMIN INTERFACE CONFIGURATION
# =============================================================================

# Admin Server Settings
ADMIN_HOST=0.0.0.0
ADMIN_PORT=8000
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123
ADMIN_SESSION_TIMEOUT=3600
ADMIN_ENABLE_METRICS=true
ADMIN_CORS_ORIGINS=*

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Logging Settings
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
LOG_FILE_PATH=logs/mignalybot_v2.log
LOG_MAX_FILE_SIZE=10485760
LOG_BACKUP_COUNT=5
LOG_ENABLE_PERFORMANCE=false

# =============================================================================
# PERFORMANCE OPTIMIZATION
# =============================================================================

# Performance Monitoring
ENABLE_PERFORMANCE_METRICS=true
PERFORMANCE_METRICS_INTERVAL=60
PERFORMANCE_MEMORY_THRESHOLD=0.8
PERFORMANCE_CPU_THRESHOLD=0.8
PERFORMANCE_GC_THRESHOLD=1000
ENABLE_PROFILING=false

# =============================================================================
# LOCALIZATION
# =============================================================================

# Default Language (en/fa)
DEFAULT_LANGUAGE=en

# Timezone
TIMEZONE=Asia/Tehran

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Debug Mode
DEBUG=false

# =============================================================================
# EXTERNAL API KEYS (Optional)
# =============================================================================

# Alpha Vantage API Key (for forex data)
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here

# CoinGecko API Key (for crypto data)
COINGECKO_API_KEY=your_coingecko_key_here

# =============================================================================
# REDIS CONFIGURATION (Optional - for advanced caching)
# =============================================================================

# Redis Settings (if using Redis for caching)
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# Security Keys
SECRET_KEY=your_secret_key_here
JWT_SECRET_KEY=your_jwt_secret_key_here

# =============================================================================
# MONITORING (Optional)
# =============================================================================

# Prometheus Metrics
PROMETHEUS_PORT=9090
PROMETHEUS_ENABLED=false

# Sentry Error Tracking
SENTRY_DSN=your_sentry_dsn_here
SENTRY_ENVIRONMENT=production
