{% extends "base.html" %}

{% block title %}Strategies - MignalyBot Admin{% endblock %}

{% block page_title %}Trading Strategies{% endblock %}

{% block extra_css %}
<!-- CodeMirror CSS -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css">
<style>
    .CodeMirror {
        height: 400px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
    
    .strategy-card {
        cursor: pointer;
        transition: transform 0.2s;
    }
    
    .strategy-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
    
    .strategy-card .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .strategy-card .badge-container {
        margin-top: 10px;
    }
    
    .strategy-card .badge {
        margin-right: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Trading Strategies</h5>
                <button type="button" class="btn btn-primary" id="addStrategyBtn">
                    <i class="fas fa-plus"></i> Add Strategy
                </button>
            </div>
            <div class="card-body">
                <div class="row" id="strategiesContainer">
                    <div class="col-12 text-center">
                        <div class="spinner-border text-primary" role="status"></div>
                        <p>Loading strategies...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Strategy Guide</h5>
            </div>
            <div class="card-body">
                <p>Trading strategies in MignalyBot are Python code that analyze market data and generate trading signals.</p>
                
                <h6 class="mt-4">Strategy Requirements:</h6>
                <ul>
                    <li>Must include a <code>generate_signals</code> function</li>
                    <li>Function must accept parameters: <code>df</code>, <code>symbol</code>, <code>timeframe</code>, <code>parameters</code></li>
                    <li>Function must return a list of signal dictionaries</li>
                </ul>
                
                <h6 class="mt-4">Available Libraries:</h6>
                <ul>
                    <li><code>pandas</code> - Data manipulation</li>
                    <li><code>numpy</code> - Numerical operations</li>
                    <li><code>pandas_ta</code> - Technical analysis indicators</li>
                </ul>
                
                <h6 class="mt-4">Signal Dictionary Format:</h6>
                <pre class="bg-light p-2 rounded">
{
    "symbol": symbol,
    "timeframe": timeframe,
    "direction": "buy" or "sell",
    "entry_price": float,
    "stop_loss": float,
    "take_profit": float,
    "notes": str  # Optional
}
                </pre>
                
                <div class="alert alert-info mt-4">
                    <i class="fas fa-info-circle"></i> Check the default strategies for examples of how to implement effective trading strategies.
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Strategy Modal -->
<div class="modal fade" id="strategyModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="strategyModalTitle">Add Strategy</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="strategyForm">
                    <input type="hidden" id="strategyId">
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="strategyName" class="form-label">Strategy Name</label>
                                <input type="text" class="form-control" id="strategyName" name="name" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="strategyDescription" class="form-label">Description</label>
                                <textarea class="form-control" id="strategyDescription" name="description" rows="3"></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="strategySymbols" class="form-label">Symbols</label>
                                <input type="text" class="form-control" id="strategySymbols" name="symbols" required>
                                <div class="form-text">Comma-separated list of symbols (e.g., BTC/USD,ETH/USD)</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="strategyTimeframes" class="form-label">Timeframes</label>
                                <input type="text" class="form-control" id="strategyTimeframes" name="timeframes" required>
                                <div class="form-text">Comma-separated list of timeframes (e.g., 1h,4h,1d)</div>
                            </div>
                            
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="strategyActive" name="active" checked>
                                <label class="form-check-label" for="strategyActive">Active</label>
                                <div class="form-text">Enable or disable this strategy</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="strategyParameters" class="form-label">Parameters (JSON)</label>
                                <textarea class="form-control" id="strategyParameters" name="parameters" rows="10" required></textarea>
                                <div class="form-text">Strategy parameters in JSON format</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="strategyType" class="form-label">Strategy Type</label>
                        <select class="form-control" id="strategyType" name="strategy_type">
                            <option value="general">General</option>
                            <option value="trend_following">Trend Following</option>
                            <option value="scalping">Scalping</option>
                            <option value="breakout">Breakout</option>
                            <option value="mean_reversion">Mean Reversion</option>
                        </select>
                        <div class="form-text">Type of trading strategy</div>
                    </div>

                    <div class="mb-3">
                        <label for="strategyPrompt" class="form-label">Strategy Prompt (AI Instructions)</label>
                        <textarea class="form-control" id="strategyPrompt" name="strategy_prompt" rows="12" required></textarea>
                        <div class="form-text">Detailed instructions for the AI to generate trading signals. Include strategy rules, entry/exit criteria, and risk management guidelines.</div>
                    </div>

                    <div class="mb-3">
                        <label for="strategyCode" class="form-label">Legacy Code (Optional)</label>
                        <textarea id="strategyCode" name="code" rows="6"></textarea>
                        <div class="form-text">Legacy code field - new strategies use AI prompts instead</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveStrategyBtn">Save Strategy</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteStrategyModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this strategy? This action cannot be undone.</p>
                <p><strong>Strategy: </strong><span id="deleteStrategyName"></span></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- CodeMirror JS -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/python/python.min.js"></script>

<script>
    let editor;
    
    $(document).ready(function() {
        // Initialize CodeMirror
        editor = CodeMirror.fromTextArea(document.getElementById("strategyCode"), {
            mode: "python",
            theme: "monokai",
            lineNumbers: true,
            indentUnit: 4,
            indentWithTabs: false,
            smartIndent: true,
            tabSize: 4,
            autoCloseBrackets: true,
            matchBrackets: true
        });
        
        // Load strategies
        loadStrategies();
        
        // Add strategy button
        $('#addStrategyBtn').click(function() {
            resetStrategyForm();
            $('#strategyModalTitle').text('Add Strategy');
            $('#strategyModal').modal('show');
        });
        
        // Save strategy button
        $('#saveStrategyBtn').click(function() {
            saveStrategy();
        });
    });
    
    function loadStrategies() {
        $.ajax({
            url: '/api/strategies',
            type: 'GET',
            success: function(data) {
                let html = '';
                
                if (data.length === 0) {
                    html = '<div class="col-12 text-center"><p>No strategies found</p></div>';
                } else {
                    data.forEach(function(strategy) {
                        const symbolBadges = strategy.symbols.split(',').map(symbol => 
                            `<span class="badge bg-primary">${symbol.trim()}</span>`
                        ).join(' ');
                        
                        const timeframeBadges = strategy.timeframes.split(',').map(timeframe => 
                            `<span class="badge bg-info">${timeframe.trim()}</span>`
                        ).join(' ');
                        
                        html += `
                            <div class="col-md-6 mb-4">
                                <div class="card strategy-card" data-strategy='${JSON.stringify(strategy)}'>
                                    <div class="card-header">
                                        <h6 class="mb-0">${strategy.name}</h6>
                                        <span class="badge ${strategy.active ? 'bg-success' : 'bg-danger'}">
                                            ${strategy.active ? 'Active' : 'Inactive'}
                                        </span>
                                    </div>
                                    <div class="card-body">
                                        <p class="card-text">${strategy.description || 'No description'}</p>
                                        <div class="badge-container">
                                            <div class="mb-2">
                                                <small class="text-muted">Symbols:</small><br>
                                                ${symbolBadges}
                                            </div>
                                            <div>
                                                <small class="text-muted">Timeframes:</small><br>
                                                ${timeframeBadges}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-footer d-flex justify-content-between">
                                        <button class="btn btn-sm btn-primary edit-strategy-btn">
                                            <i class="fas fa-edit"></i> Edit
                                        </button>
                                        <button class="btn btn-sm btn-danger delete-strategy-btn" data-strategy-id="${strategy.id}" data-strategy-name="${strategy.name}">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                }
                
                $('#strategiesContainer').html(html);
                
                // Set up edit button handlers
                $('.strategy-card').click(function(e) {
                    if (!$(e.target).closest('button').length) {
                        const strategy = $(this).data('strategy');
                        editStrategy(strategy);
                    }
                });
                
                $('.edit-strategy-btn').click(function(e) {
                    e.stopPropagation();
                    const strategy = $(this).closest('.strategy-card').data('strategy');
                    editStrategy(strategy);
                });
                
                // Set up delete button handlers
                $('.delete-strategy-btn').click(function(e) {
                    e.stopPropagation();
                    const strategyId = $(this).data('strategy-id');
                    const strategyName = $(this).data('strategy-name');
                    showDeleteConfirmation(strategyId, strategyName);
                });
            },
            error: function(xhr) {
                alert('Error loading strategies: ' + xhr.responseJSON.detail);
            }
        });
    }
    
    function resetStrategyForm() {
        $('#strategyId').val('');
        $('#strategyForm')[0].reset();
        
        // Set default parameters
        $('#strategyParameters').val(JSON.stringify({
            "risk_percentage": 2.0,
            "reward_ratio": 2.0,
            "confidence_threshold": 0.7
        }, null, 2));

        // Set default strategy type and prompt
        $('#strategyType').val('general');
        $('#strategyPrompt').val(`You are a professional trader. Analyze the provided market data and generate trading signals.

STRATEGY RULES:
1. Look for clear market trends and patterns
2. Use proper risk management with stop losses
3. Target 2:1 risk-reward ratio minimum
4. Only trade when confidence is high

SIGNAL CRITERIA:
- Clear entry and exit points
- Proper risk management
- Market context consideration

Generate BUY signals when:
- Strong bullish momentum
- Break above key resistance
- Trend continuation patterns

Generate SELL signals when:
- Strong bearish momentum
- Break below key support
- Trend reversal patterns

RESPONSE FORMAT:
If signal found, respond with JSON:
{
  "signal": true,
  "direction": "buy" or "sell",
  "entry_price": current_price,
  "stop_loss": calculated_sl,
  "take_profit": calculated_tp,
  "confidence": 0.0-1.0,
  "reasoning": "detailed explanation"
}

If no signal, respond with:
{
  "signal": false,
  "reasoning": "why no signal"
}`);
        
        // Set default code
        const defaultCode = `def generate_signals(df, symbol, timeframe, parameters):
    """
    Generate trading signals based on EMA crossover
    
    Args:
        df (pandas.DataFrame): Candle data
        symbol (str): Symbol being processed
        timeframe (str): Timeframe being processed
        parameters (dict): Strategy parameters
    
    Returns:
        list: List of trading signals
    """
    # Get parameters
    fast_ema = parameters.get("fast_ema", 9)
    slow_ema = parameters.get("slow_ema", 21)
    atr_period = parameters.get("atr_period", 14)
    atr_multiplier = parameters.get("atr_multiplier", 2.0)
    
    # Calculate indicators
    df["fast_ema"] = df["close"].ewm(span=fast_ema, adjust=False).mean()
    df["slow_ema"] = df["close"].ewm(span=slow_ema, adjust=False).mean()
    
    # Calculate ATR for stop loss
    df["high_low"] = df["high"] - df["low"]
    df["high_close"] = abs(df["high"] - df["close"].shift())
    df["low_close"] = abs(df["low"] - df["close"].shift())
    df["tr"] = df[["high_low", "high_close", "low_close"]].max(axis=1)
    df["atr"] = df["tr"].rolling(window=atr_period).mean()
    
    # Generate signals
    signals = []
    
    # Check only the last 3 candles for new signals
    for i in range(len(df) - 3, len(df)):
        # Skip if not enough data
        if i <= 0:
            continue
        
        # Buy signal: fast EMA crosses above slow EMA
        if df["fast_ema"].iloc[i-1] <= df["slow_ema"].iloc[i-1] and df["fast_ema"].iloc[i] > df["slow_ema"].iloc[i]:
            entry_price = df["close"].iloc[i]
            stop_loss = entry_price - (df["atr"].iloc[i] * atr_multiplier)
            take_profit = entry_price + (df["atr"].iloc[i] * atr_multiplier * 1.5)
            
            signals.append({
                "symbol": symbol,
                "timeframe": timeframe,
                "direction": "buy",
                "entry_price": entry_price,
                "stop_loss": stop_loss,
                "take_profit": take_profit,
                "notes": f"EMA Crossover: {fast_ema} EMA crossed above {slow_ema} EMA"
            })
        
        # Sell signal: fast EMA crosses below slow EMA
        elif df["fast_ema"].iloc[i-1] >= df["slow_ema"].iloc[i-1] and df["fast_ema"].iloc[i] < df["slow_ema"].iloc[i]:
            entry_price = df["close"].iloc[i]
            stop_loss = entry_price + (df["atr"].iloc[i] * atr_multiplier)
            take_profit = entry_price - (df["atr"].iloc[i] * atr_multiplier * 1.5)
            
            signals.append({
                "symbol": symbol,
                "timeframe": timeframe,
                "direction": "sell",
                "entry_price": entry_price,
                "stop_loss": stop_loss,
                "take_profit": take_profit,
                "notes": f"EMA Crossover: {fast_ema} EMA crossed below {slow_ema} EMA"
            })
    
    return signals`;
        
        editor.setValue(defaultCode);
    }
    
    function editStrategy(strategy) {
        resetStrategyForm();
        
        $('#strategyId').val(strategy.id);
        $('#strategyName').val(strategy.name);
        $('#strategyDescription').val(strategy.description);
        $('#strategySymbols').val(strategy.symbols);
        $('#strategyTimeframes').val(strategy.timeframes);
        $('#strategyActive').prop('checked', strategy.active);
        $('#strategyParameters').val(JSON.stringify(strategy.parameters, null, 2));
        $('#strategyType').val(strategy.strategy_type || 'general');
        $('#strategyPrompt').val(strategy.strategy_prompt || '');
        editor.setValue(strategy.code || '');
        
        $('#strategyModalTitle').text('Edit Strategy');
        $('#strategyModal').modal('show');
    }
    
    function saveStrategy() {
        // Update code from editor
        editor.save();
        
        const strategyId = $('#strategyId').val();
        
        // Parse parameters JSON
        let parameters;
        try {
            parameters = JSON.parse($('#strategyParameters').val());
        } catch (e) {
            alert('Invalid JSON in parameters field');
            return;
        }
        
        const formData = {
            name: $('#strategyName').val(),
            description: $('#strategyDescription').val(),
            symbols: $('#strategySymbols').val(),
            timeframes: $('#strategyTimeframes').val(),
            active: $('#strategyActive').is(':checked'),
            parameters: parameters,
            strategy_prompt: $('#strategyPrompt').val(),
            strategy_type: $('#strategyType').val(),
            code: $('#strategyCode').val()
        };
        
        const url = strategyId ? `/api/strategies/${strategyId}` : '/api/strategies';
        const method = strategyId ? 'PUT' : 'POST';
        
        $.ajax({
            url: url,
            type: method,
            contentType: 'application/json',
            data: JSON.stringify(formData),
            success: function(response) {
                $('#strategyModal').modal('hide');
                loadStrategies();
                alert(`Strategy ${strategyId ? 'updated' : 'created'} successfully!`);
            },
            error: function(xhr) {
                alert(`Error ${strategyId ? 'updating' : 'creating'} strategy: ` + xhr.responseJSON.detail);
            }
        });
    }
    
    function showDeleteConfirmation(strategyId, strategyName) {
        $('#deleteStrategyName').text(strategyName);
        $('#confirmDeleteBtn').data('strategy-id', strategyId);
        $('#deleteStrategyModal').modal('show');
        
        // Set up delete confirmation button
        $('#confirmDeleteBtn').off('click').click(function() {
            const id = $(this).data('strategy-id');
            deleteStrategy(id);
        });
    }
    
    function deleteStrategy(strategyId) {
        $.ajax({
            url: `/api/strategies/${strategyId}`,
            type: 'DELETE',
            success: function(response) {
                $('#deleteStrategyModal').modal('hide');
                loadStrategies();
                alert('Strategy deleted successfully!');
            },
            error: function(xhr) {
                alert('Error deleting strategy: ' + xhr.responseJSON.detail);
            }
        });
    }
</script>
{% endblock %}
