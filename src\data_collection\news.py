"""
News collection module for MignalyBot
"""

import os
import logging
import asyncio
import re
import feedparser
import httpx
from datetime import datetime, timedelta, timezone
from bs4 import BeautifulSoup
from sqlalchemy import select

from src.database.setup import get_async_db, is_sqlite_db
from src.database.models import NewsItem

logger = logging.getLogger(__name__)

async def search_relevant_image(title, content=""):
    """
    Search for a relevant image based on news title and content

    Args:
        title (str): News title
        content (str): News content (optional)

    Returns:
        str: Image URL if found, None otherwise
    """
    try:
        # Extract keywords from title for image search
        keywords = extract_image_keywords(title, content)

        if not keywords:
            return None

        # Try multiple image search strategies
        image_url = await search_unsplash_image(keywords)
        if image_url:
            return image_url

        # Fallback to Pexels if Unsplash fails
        image_url = await search_pexels_image(keywords)
        if image_url:
            return image_url

        return None

    except Exception as e:
        logger.debug(f"Error searching for relevant image: {e}")
        return None

def extract_image_keywords(title, content=""):
    """
    Extract relevant keywords for image search from news title and content

    Args:
        title (str): News title
        content (str): News content

    Returns:
        str: Search keywords
    """
    import re

    # Financial keywords mapping
    financial_keywords = {
        # Currencies
        'USD': 'dollar currency',
        'EUR': 'euro currency',
        'GBP': 'pound sterling currency',
        'JPY': 'yen currency',
        'CHF': 'swiss franc currency',
        'CAD': 'canadian dollar currency',
        'AUD': 'australian dollar currency',
        'NZD': 'new zealand dollar currency',

        # Economic terms
        'inflation': 'inflation economy chart',
        'GDP': 'economic growth chart',
        'employment': 'employment jobs economy',
        'unemployment': 'unemployment jobs crisis',
        'interest rate': 'federal reserve bank',
        'central bank': 'central bank building',
        'fed': 'federal reserve',
        'ECB': 'european central bank',
        'BOE': 'bank of england',
        'BOJ': 'bank of japan',

        # Market terms
        'stock': 'stock market trading',
        'forex': 'forex trading currency',
        'trading': 'financial trading charts',
        'market': 'financial market',
        'bull': 'bull market finance',
        'bear': 'bear market finance',
        'rally': 'market rally green charts',
        'crash': 'market crash red charts',
        'volatility': 'volatile market charts',

        # Economic indicators
        'CPI': 'consumer price index chart',
        'PPI': 'producer price index',
        'PMI': 'purchasing managers index',
        'NFP': 'non farm payrolls employment',
        'retail sales': 'retail sales economy',
        'housing': 'housing market real estate',

        # Geopolitical
        'tariff': 'trade war tariffs',
        'trade': 'international trade',
        'sanctions': 'economic sanctions',
        'brexit': 'brexit uk europe',
        'election': 'political election',
        'war': 'geopolitical conflict',
        'oil': 'oil prices energy',
        'gold': 'gold precious metals',
        'crypto': 'cryptocurrency bitcoin',
        'bitcoin': 'bitcoin cryptocurrency'
    }

    text = (title + " " + content).lower()

    # Find matching keywords
    matched_keywords = []
    for key, value in financial_keywords.items():
        if key.lower() in text:
            matched_keywords.append(value)

    # If no specific matches, use general financial terms
    if not matched_keywords:
        if any(word in text for word in ['bank', 'economy', 'financial', 'money', 'currency']):
            matched_keywords.append('financial market economy')
        elif any(word in text for word in ['trade', 'export', 'import']):
            matched_keywords.append('international trade business')
        else:
            matched_keywords.append('business finance economy')

    return ' '.join(matched_keywords[:2])  # Limit to 2 keyword groups

async def search_unsplash_image(keywords):
    """
    Search for images on Unsplash with better variety

    Args:
        keywords (str): Search keywords

    Returns:
        str: Image URL if found, None otherwise
    """
    try:
        # Use Unsplash's random image API with specific collections for financial content
        # This provides more variety than the source API
        keywords_clean = keywords.replace(' ', ',').lower()

        # Try different approaches for better image variety
        image_urls = []

        # Approach 1: Use specific financial collections
        if any(word in keywords_clean for word in ['forex', 'currency', 'dollar', 'euro', 'pound']):
            image_urls.append("https://images.unsplash.com/photo-*************-9c2a0a7236a3?w=800&h=600&fit=crop&crop=center")  # Currency
            image_urls.append("https://images.unsplash.com/photo-**********-b33ff0c44a43?w=800&h=600&fit=crop&crop=center")  # Money
        elif any(word in keywords_clean for word in ['chart', 'graph', 'analysis', 'market']):
            image_urls.append("https://images.unsplash.com/photo-*************-17ffb3a7f29f?w=800&h=600&fit=crop&crop=center")  # Charts
            image_urls.append("https://images.unsplash.com/photo-**********-bebda4e38f71?w=800&h=600&fit=crop&crop=center")  # Data
        elif any(word in keywords_clean for word in ['crypto', 'bitcoin', 'ethereum']):
            image_urls.append("https://images.unsplash.com/photo-*************-5a555bb7020d?w=800&h=600&fit=crop&crop=center")  # Crypto
            image_urls.append("https://images.unsplash.com/photo-*************-c6fb62004040?w=800&h=600&fit=crop&crop=center")  # Bitcoin
        elif any(word in keywords_clean for word in ['bank', 'fed', 'central', 'rate']):
            image_urls.append("https://images.unsplash.com/photo-*************-f4d9a9f9297f?w=800&h=600&fit=crop&crop=center")  # Banking
            image_urls.append("https://images.unsplash.com/photo-**********-6726b3ff858f?w=800&h=600&fit=crop&crop=center")  # Finance
        else:
            # General financial images
            image_urls.extend([
                "https://images.unsplash.com/photo-*************-17ffb3a7f29f?w=800&h=600&fit=crop&crop=center",
                "https://images.unsplash.com/photo-*************-9c2a0a7236a3?w=800&h=600&fit=crop&crop=center",
                "https://images.unsplash.com/photo-**********-bebda4e38f71?w=800&h=600&fit=crop&crop=center"
            ])

        # Select a random image from the relevant category
        import random
        if image_urls:
            selected_url = random.choice(image_urls)
            logger.debug(f"Selected Unsplash image for keywords '{keywords}': {selected_url}")
            return selected_url

        return None

    except Exception as e:
        logger.debug(f"Error searching Unsplash for '{keywords}': {e}")
        return None

async def search_pexels_image(keywords):
    """
    Search for images on Pexels (fallback) with better variety

    Args:
        keywords (str): Search keywords

    Returns:
        str: Image URL if found, None otherwise
    """
    try:
        import random
        keywords_clean = keywords.lower()

        # Expanded collection of financial images from Pexels
        financial_images = {
            'currency': [
                "https://images.pexels.com/photos/159888/pexels-photo-159888.jpeg?auto=compress&cs=tinysrgb&w=800&h=600",  # Money
                "https://images.pexels.com/photos/164527/pexels-photo-164527.jpeg?auto=compress&cs=tinysrgb&w=800&h=600",  # Coins
                "https://images.pexels.com/photos/259027/pexels-photo-259027.jpeg?auto=compress&cs=tinysrgb&w=800&h=600",  # Cash
            ],
            'charts': [
                "https://images.pexels.com/photos/730547/pexels-photo-730547.jpeg?auto=compress&cs=tinysrgb&w=800&h=600",  # Charts
                "https://images.pexels.com/photos/590041/pexels-photo-590041.jpeg?auto=compress&cs=tinysrgb&w=800&h=600",  # Graph
                "https://images.pexels.com/photos/669610/pexels-photo-669610.jpeg?auto=compress&cs=tinysrgb&w=800&h=600",  # Analytics
            ],
            'business': [
                "https://images.pexels.com/photos/186461/pexels-photo-186461.jpeg?auto=compress&cs=tinysrgb&w=800&h=600",  # Business
                "https://images.pexels.com/photos/534216/pexels-photo-534216.jpeg?auto=compress&cs=tinysrgb&w=800&h=600",  # Office
                "https://images.pexels.com/photos/416405/pexels-photo-416405.jpeg?auto=compress&cs=tinysrgb&w=800&h=600",  # Meeting
            ],
            'trading': [
                "https://images.pexels.com/photos/210607/pexels-photo-210607.jpeg?auto=compress&cs=tinysrgb&w=800&h=600",  # Trading
                "https://images.pexels.com/photos/187041/pexels-photo-187041.jpeg?auto=compress&cs=tinysrgb&w=800&h=600",  # Stock
                "https://images.pexels.com/photos/844124/pexels-photo-844124.jpeg?auto=compress&cs=tinysrgb&w=800&h=600",  # Finance
            ]
        }

        # Select category based on keywords
        if any(word in keywords_clean for word in ['currency', 'dollar', 'euro', 'pound', 'money']):
            selected_images = financial_images['currency']
        elif any(word in keywords_clean for word in ['chart', 'graph', 'analysis', 'market', 'data']):
            selected_images = financial_images['charts']
        elif any(word in keywords_clean for word in ['trading', 'trader', 'stock', 'forex']):
            selected_images = financial_images['trading']
        else:
            selected_images = financial_images['business']

        # Return a random image from the selected category
        selected_url = random.choice(selected_images)
        logger.debug(f"Selected Pexels image for keywords '{keywords}': {selected_url}")
        return selected_url

    except Exception as e:
        logger.debug(f"Error searching Pexels for '{keywords}': {e}")
        return None

async def extract_image_from_article(article_url):
    """
    Extract the main image from a news article page

    Args:
        article_url (str): URL of the article

    Returns:
        str: Image URL if found, None otherwise
    """
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get(article_url, headers={
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            })
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')

            # Try multiple strategies to find the main article image

            # Strategy 1: Look for Open Graph image
            og_image = soup.find('meta', property='og:image')
            if og_image and og_image.get('content'):
                return og_image['content']

            # Strategy 2: Look for Twitter card image
            twitter_image = soup.find('meta', attrs={'name': 'twitter:image'})
            if twitter_image and twitter_image.get('content'):
                return twitter_image['content']

            # Strategy 3: Look for the first image in article content
            article_selectors = [
                'article img',
                '.article-content img',
                '.post-content img',
                '.entry-content img',
                '.content img',
                'main img'
            ]

            for selector in article_selectors:
                img = soup.select_one(selector)
                if img and img.get('src'):
                    img_src = img['src']
                    # Make sure it's a full URL
                    if img_src.startswith('//'):
                        img_src = 'https:' + img_src
                    elif img_src.startswith('/'):
                        from urllib.parse import urljoin
                        img_src = urljoin(article_url, img_src)

                    # Skip very small images (likely icons)
                    if 'icon' not in img_src.lower() and 'logo' not in img_src.lower():
                        return img_src

            return None

    except Exception as e:
        logger.debug(f"Error extracting image from article {article_url}: {e}")
        return None

# List of RSS feeds for financial news - prioritized for forex content
NEWS_FEEDS = [
    {
        "url": "https://investinglive.com/feed",
        "source": "investingLive (formerly ForexLive)",
        "priority": 1,  # Updated URL - ForexLive rebranded to investingLive
        "timeout": 20,
        "headers": {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Accept": "application/rss+xml, application/xml, text/xml, */*",
            "Accept-Language": "en-US,en;q=0.9",
            "Cache-Control": "no-cache",
            "Pragma": "no-cache",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none"
        }
    },
    {
        "url": "https://www.investing.com/rss/news_285.rss",  # Forex specific feed
        "source": "Investing.com Forex",
        "priority": 1,
        "timeout": 15  # Shorter timeout for reliable feeds
    },
    {
        "url": "https://www.fxstreet.com/rss",
        "source": "FXStreet",
        "priority": 1,
        "timeout": 15
    },
    {
        "url": "https://www.dailyfx.com/feeds/market-news",
        "source": "DailyFX",
        "priority": 3,  # Moved to lower priority due to 403 errors
        "timeout": 15,
        "headers": {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Accept": "application/rss+xml, application/xml, text/xml, */*",
            "Accept-Language": "en-US,en;q=0.9",
            "Referer": "https://www.dailyfx.com/",
            "Cache-Control": "no-cache"
        }
    },
    {
        "url": "https://www.investing.com/rss/news.rss",
        "source": "Investing.com",
        "priority": 2,
        "timeout": 15
    },
    {
        "url": "https://feeds.marketwatch.com/marketwatch/topstories/",
        "source": "MarketWatch",
        "priority": 2,
        "timeout": 15
    },
    {
        "url": "https://www.reuters.com/business/finance/rss",
        "source": "Reuters Finance",
        "priority": 2,
        "timeout": 15
    },
    {
        "url": "https://finance.yahoo.com/rss/topstories",
        "source": "Yahoo Finance",
        "priority": 3,
        "timeout": 15
    },
    {
        "url": "https://cointelegraph.com/rss",
        "source": "CoinTelegraph",
        "priority": 3,
        "timeout": 15
    },
    {
        "url": "https://www.coindesk.com/arc/outboundfeeds/rss/",
        "source": "CoinDesk",
        "priority": 3,
        "timeout": 15
    },
    # Additional reliable forex feeds
    {
        "url": "https://www.forexfactory.com/rss.php",
        "source": "ForexFactory RSS",
        "priority": 1,
        "timeout": 15
    },
    {
        "url": "https://www.babypips.com/rss",
        "source": "BabyPips",
        "priority": 2,
        "timeout": 15
    }
]

async def collect_news(symbols=None):
    """
    Collect financial news from various sources

    Args:
        symbols (list, optional): List of symbols to filter news for. Defaults to None.
    """
    logger.info("Collecting financial news")

    # Default to empty list if symbols is None
    if symbols is None:
        symbols = []
        # Try to get symbols from config
        try:
            async for db in get_async_db():
                from src.database.models import Config
                from sqlalchemy import select

                # Get config
                if is_sqlite_db():
                    result = db.execute(select(Config))
                else:
                    result = await db.execute(select(Config))

                config = result.scalars().first()

                if config and config.symbols:
                    symbols = config.symbols.split(",")
        except Exception as e:
            logger.error(f"Error getting symbols from config: {e}", exc_info=True)
            # Default to some common symbols
            symbols = ["BTC/USD", "ETH/USD", "EUR/USD", "GBP/USD"]

    # Get news from RSS feeds - prioritize forex feeds
    # Sort feeds by priority and take top 3 feeds
    sorted_feeds = sorted(NEWS_FEEDS, key=lambda x: x.get('priority', 999))
    for feed_info in sorted_feeds[:3]:  # Use top 3 priority feeds
        try:
            feed_url = feed_info["url"]
            source = feed_info["source"]

            logger.info(f"Fetching news from {source}")

            # Parse RSS feed with retry logic
            feed = None
            max_retries = 3
            timeout = feed_info.get("timeout", 15)  # Default to 15 seconds if not specified

            for attempt in range(max_retries):
                try:
                    loop = asyncio.get_event_loop()

                    # Use custom headers for each feed if available, otherwise use default
                    default_headers = {
                        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                        "Accept": "application/rss+xml, application/xml, text/xml, */*",
                        "Accept-Language": "en-US,en;q=0.9",
                        "Cache-Control": "no-cache",
                        "Pragma": "no-cache"
                    }

                    # Use feed-specific headers if available
                    headers = feed_info.get("headers", default_headers)

                    # Use httpx to fetch the RSS content first, then parse with feedparser
                    # Use a shorter timeout for more reliable feeds
                    logger.debug(f"Attempting to fetch {source} with timeout {timeout}s (attempt {attempt + 1}/{max_retries})")

                    try:
                        async with httpx.AsyncClient(timeout=timeout) as client:
                            response = await client.get(feed_url, headers=headers)
                            response.raise_for_status()
                            rss_content = response.text
                    except httpx.TimeoutException:
                        logger.warning(f"Timeout ({timeout}s) reached for {source} on attempt {attempt + 1}")
                        if attempt == max_retries - 1:
                            logger.error(f"All {max_retries} attempts timed out for {source}, skipping this feed")
                            break
                        # Increase timeout for next attempt
                        timeout = min(timeout * 1.5, 60)  # Cap at 60 seconds
                        await asyncio.sleep(2 ** attempt)  # Exponential backoff
                        continue
                    except httpx.HTTPStatusError as e:
                        logger.warning(f"HTTP error {e.response.status_code} for {source} on attempt {attempt + 1}")
                        if e.response.status_code in [403, 429]:  # Forbidden or rate limited
                            # Wait longer for rate limiting issues
                            await asyncio.sleep(5 * (2 ** attempt))
                        else:
                            await asyncio.sleep(2 ** attempt)
                        continue
                    except Exception as e:
                        error_message = str(e).lower()
                        logger.warning(f"Request error for {source} on attempt {attempt + 1}: {type(e).__name__}: {e}")

                        # Special handling for DNS resolution failures
                        if "temporary failure in name resolution" in error_message:
                            logger.warning("🌐 DNS resolution failure - network connectivity issue")
                            logger.warning("💡 This usually indicates internet connectivity problems or DNS server issues")
                            await asyncio.sleep(10 * (2 ** attempt))  # Longer backoff for DNS issues
                        # For connection errors, wait longer
                        elif "connection" in error_message or "ssl" in error_message:
                            await asyncio.sleep(5 * (2 ** attempt))
                        else:
                            await asyncio.sleep(2 ** attempt)
                        continue

                    # Parse the RSS content with feedparser
                    try:
                        # Parse directly without executor to avoid potential issues
                        feed = feedparser.parse(rss_content)

                        # Validate feed structure
                        if not hasattr(feed, 'entries') or not feed.entries:
                            logger.warning(f"Feed {source} has no entries, may be malformed")
                            if hasattr(feed, 'bozo') and feed.bozo:
                                logger.debug(f"Feed parsing warning: {feed.bozo_exception}")
                            if attempt == max_retries - 1:
                                break
                            await asyncio.sleep(2 ** attempt)
                            continue

                        # Success, exit retry loop
                        logger.info(f"Successfully fetched and parsed {source} feed with {len(feed.entries)} entries")
                        break
                    except Exception as e:
                        logger.warning(f"Failed to parse {source} feed: {e}")
                        if attempt == max_retries - 1:
                            break
                        await asyncio.sleep(2 ** attempt)
                        continue

                except Exception as e:
                    logger.warning(f"Attempt {attempt + 1} failed for {source}: {e}")
                    if attempt == max_retries - 1:
                        logger.error(f"All {max_retries} attempts failed for {source}, skipping this feed")
                        feed = None  # Set feed to None to indicate failure
                        break  # Exit retry loop
                    await asyncio.sleep(2 ** attempt)  # Exponential backoff

            # Check if feed was successfully fetched
            if not feed or not hasattr(feed, 'entries') or not feed.entries:
                logger.warning(f"No entries found in feed: {source}")
                continue

            # Process each news item
            for entry in feed.entries[:5]:  # Limit to 5 most recent entries to reduce load
                try:
                    # Extract data from feed entry
                    title = entry.title
                    link = entry.link

                    # Parse published date with error handling
                    published_time = datetime.now(timezone.utc)  # Default fallback
                    try:
                        if hasattr(entry, 'published_parsed') and entry.published_parsed:
                            if len(entry.published_parsed) >= 6:
                                published_time = datetime(*entry.published_parsed[:6], tzinfo=timezone.utc)
                        elif hasattr(entry, 'updated_parsed') and entry.updated_parsed:
                            if len(entry.updated_parsed) >= 6:
                                published_time = datetime(*entry.updated_parsed[:6], tzinfo=timezone.utc)
                    except (TypeError, ValueError) as e:
                        logger.warning(f"Error parsing date for entry '{title}': {e}, using current time")
                        published_time = datetime.now(timezone.utc)

                    # Skip news older than 24 hours
                    # Make sure both datetimes are timezone-aware for comparison
                    if not isinstance(published_time, datetime):
                        # If it's not a datetime object, skip this check
                        pass
                    elif published_time.tzinfo is None:
                        # If it's a naive datetime, add timezone info
                        published_time = published_time.replace(tzinfo=timezone.utc)
                        if published_time < datetime.now(timezone.utc) - timedelta(days=1):
                            continue
                    else:
                        # If it's already timezone-aware
                        if published_time < datetime.now(timezone.utc) - timedelta(days=1):
                            continue

                    # Extract content
                    content = ""
                    if hasattr(entry, 'summary'):
                        content = entry.summary
                    elif hasattr(entry, 'description'):
                        content = entry.description

                    # Clean content (remove HTML)
                    if content:
                        soup = BeautifulSoup(content, 'html.parser')
                        content = soup.get_text()

                    # Extract image URL with multiple fallback methods
                    image_url = None

                    # Method 1: Check media_content
                    if hasattr(entry, 'media_content') and entry.media_content:
                        for media in entry.media_content:
                            if 'url' in media:
                                image_url = media['url']
                                break

                    # Method 2: Check links for image types
                    if not image_url and hasattr(entry, 'links'):
                        for link in entry.links:
                            if link.get('type', '').startswith('image/'):
                                image_url = link.get('href')
                                break

                    # Method 3: Check for enclosures (common in RSS feeds)
                    if not image_url and hasattr(entry, 'enclosures'):
                        for enclosure in entry.enclosures:
                            if hasattr(enclosure, 'type') and enclosure.type.startswith('image/'):
                                image_url = enclosure.href
                                break

                    # Method 4: Look for images in the content/summary
                    if not image_url and content:
                        # Look for img tags in HTML content
                        img_match = re.search(r'<img[^>]+src=["\']([^"\']+)["\']', content)
                        if img_match:
                            image_url = img_match.group(1)

                    # Method 5: Check for thumbnail or media_thumbnail
                    if not image_url:
                        if hasattr(entry, 'media_thumbnail') and entry.media_thumbnail:
                            if isinstance(entry.media_thumbnail, list) and len(entry.media_thumbnail) > 0:
                                image_url = entry.media_thumbnail[0].get('url')
                            elif hasattr(entry.media_thumbnail, 'url'):
                                image_url = entry.media_thumbnail.url
                        elif hasattr(entry, 'thumbnail') and entry.thumbnail:
                            image_url = entry.thumbnail

                    # Method 6: Try to extract image from the actual article page
                    if not image_url and link:
                        try:
                            article_url = link.get('href') if isinstance(link, dict) else link
                            image_url = await extract_image_from_article(article_url)
                            if image_url:
                                logger.debug(f"Extracted image from article page for '{title}': {image_url}")
                        except Exception as e:
                            logger.debug(f"Failed to extract image from article page for '{title}': {e}")

                    # Method 7: Search for relevant image based on news content
                    if not image_url:
                        try:
                            # Get news content for better keyword extraction
                            news_content = summary if summary else description if description else ""
                            image_url = await search_relevant_image(title, news_content)
                            if image_url:
                                logger.info(f"Found relevant image for news '{title[:50]}...': {image_url}")
                        except Exception as e:
                            logger.debug(f"Failed to search relevant image for '{title}': {e}")

                    # Method 8: Use a variety of default financial news images if no image found
                    if not image_url:
                        import random
                        # Use a variety of default financial images from reliable sources
                        default_images = [
                            "https://images.unsplash.com/photo-*************-17ffb3a7f29f?w=800&h=600&fit=crop&crop=center",  # Charts
                            "https://images.unsplash.com/photo-*************-9c2a0a7236a3?w=800&h=600&fit=crop&crop=center",  # Currency
                            "https://images.unsplash.com/photo-**********-bebda4e38f71?w=800&h=600&fit=crop&crop=center",  # Data
                            "https://images.unsplash.com/photo-**********-b33ff0c44a43?w=800&h=600&fit=crop&crop=center",  # Money
                            "https://images.unsplash.com/photo-*************-5a555bb7020d?w=800&h=600&fit=crop&crop=center",  # Crypto
                            "https://images.unsplash.com/photo-*************-f4d9a9f9297f?w=800&h=600&fit=crop&crop=center",  # Banking
                        ]
                        image_url = random.choice(default_images)
                        logger.debug(f"Using random default financial image for news '{title}': {image_url}")

                    # Log image URL extraction for debugging
                    if image_url:
                        logger.debug(f"Found/assigned image URL for news '{title}': {image_url}")
                    else:
                        logger.debug(f"No image URL found for news '{title}'")

                    # Check forex relevance
                    forex_relevance = calculate_forex_relevance(title + " " + (content or ""))

                    # Skip news with very low forex relevance unless it's from a high-priority forex source
                    if forex_relevance < 0.3 and feed_info.get('priority', 999) > 1:
                        logger.debug(f"Skipping low forex relevance news: {title} (relevance: {forex_relevance:.2f})")
                        continue

                    # Determine related symbols
                    related_symbols = []
                    for symbol in symbols:
                        # Remove the slash for matching
                        clean_symbol = symbol.replace("/", "")
                        if clean_symbol in title or clean_symbol in content:
                            related_symbols.append(symbol)

                    # Save news item to database
                    # Extract URL string from link object if it's a dictionary
                    url_str = link.get('href') if isinstance(link, dict) else link

                    # Use a separate try/except block for saving to isolate database errors
                    try:
                        await save_news_item(
                            title=title,
                            content=content,
                            source=source,
                            url=url_str,
                            image_url=image_url,
                            published_at=published_time,
                            symbols=",".join(related_symbols) if related_symbols else None
                        )
                    except Exception as e:
                        logger.error(f"Error saving news item: {e}", exc_info=True)
                        # Continue with next item instead of failing the whole process

                except Exception as e:
                    logger.error(f"Error processing news entry: {e}", exc_info=True)

        except Exception as e:
            logger.error(f"Error fetching news from {source}: {e}", exc_info=True)

    logger.info("News collection completed")

async def save_news_item(title, content, source, url, image_url, published_at, symbols):
    """
    Save news item to database

    Args:
        title (str): News title
        content (str): News content
        source (str): News source
        url (str): News URL
        image_url (str): URL of news image
        published_at (datetime): Publication date
        symbols (str): Comma-separated list of related symbols
    """
    async for db in get_async_db():
        try:
            # Check if news item already exists
            if is_sqlite_db():
                result = db.execute(
                    select(NewsItem).where(
                        NewsItem.title == title,
                        NewsItem.source == source
                    )
                )
            else:
                result = await db.execute(
                    select(NewsItem).where(
                        NewsItem.title == title,
                        NewsItem.source == source
                    )
                )

            existing_news = result.scalars().first()

            if existing_news:
                logger.debug(f"News item already exists: {title}")
                return

            # Create new news item
            news_item = NewsItem(
                title=title,
                content=content,
                source=source,
                url=url,
                image_url=image_url,
                published_at=published_at,
                symbols=symbols,
                created_at=datetime.now(timezone.utc)
            )

            db.add(news_item)
            if is_sqlite_db():
                db.commit()
            else:
                await db.commit()

            logger.info(f"Saved news item: {title}")

        except Exception as e:
            if is_sqlite_db():
                db.rollback()
            else:
                await db.rollback()
            logger.error(f"Error saving news item: {e}", exc_info=True)
            raise


def calculate_forex_relevance(text):
    """
    Calculate how relevant a news article is to forex trading
    Returns a score between 0 and 1
    """
    text_lower = text.lower()

    # High-value forex keywords
    high_value_keywords = [
        'forex', 'currency', 'exchange rate', 'central bank', 'fed', 'ecb', 'boj', 'boe',
        'interest rate', 'monetary policy', 'inflation', 'gdp', 'unemployment',
        'dollar', 'euro', 'yen', 'pound', 'franc', 'yuan', 'cad', 'aud', 'nzd',
        'usd', 'eur', 'jpy', 'gbp', 'chf', 'cny', 'nok', 'sek', 'dkk',
        'eurusd', 'gbpusd', 'usdjpy', 'usdchf', 'audusd', 'usdcad', 'nzdusd',
        'trade war', 'tariff', 'economic data', 'nonfarm payroll', 'cpi', 'ppi'
    ]

    # Medium-value keywords
    medium_value_keywords = [
        'economy', 'economic', 'financial', 'market', 'trading', 'investment',
        'recession', 'growth', 'stimulus', 'bailout', 'debt', 'deficit',
        'export', 'import', 'balance', 'surplus'
    ]

    score = 0.0

    # Count high-value keywords (weight: 0.1 each)
    for keyword in high_value_keywords:
        if keyword in text_lower:
            score += 0.1

    # Count medium-value keywords (weight: 0.05 each)
    for keyword in medium_value_keywords:
        if keyword in text_lower:
            score += 0.05

    # Cap the score at 1.0
    return min(score, 1.0)
