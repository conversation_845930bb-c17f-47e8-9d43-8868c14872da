"""
Economic calendar data collection module for MignalyBot
Uses Forex Factory API for economic events
"""

import logging
import asyncio
import httpx
import re
import random
from datetime import datetime, timezone, timedelta
from sqlalchemy import select
from bs4 import BeautifulSoup

from src.database.setup import get_async_db, is_sqlite_db
from src.database.models import EconomicEvent
from src.utils.network_utils import create_http_client, retry_with_backoff, NetworkError

logger = logging.getLogger(__name__)

# Forex Factory API endpoint for economic events
FOREX_FACTORY_API_URL = "https://nfs.faireconomy.media/ff_calendar_thisweek.json"

# Alternative endpoints in case the primary fails
ALTERNATIVE_ENDPOINTS = [
    "https://nfs.faireconomy.media/ff_calendar_thisweek.json",
    "https://nfs.faireconomy.media/ff_calendar_nextweek.json",  # Next week data as fallback
    "https://www.forexfactory.com/calendar.php?week=this",  # Fallback (requires different parsing)
    "https://www.forexfactory.com/calendar",  # Additional fallback
]

# Timeout configurations
DEFAULT_TIMEOUT = 30.0
CONNECT_TIMEOUT = 10.0
READ_TIMEOUT = 30.0

# User agents to rotate for avoiding blocks
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0"
]

async def collect_economic_events():
    """Collect economic calendar events from Forex Factory API"""
    logger.info("Collecting economic calendar events from Forex Factory API")

    try:
        # Fetch events from Forex Factory API
        events = await fetch_events_from_forex_factory()

        if events:
            logger.info(f"Processing {len(events)} economic events")
            saved_count = 0

            for event in events:
                try:
                    # Check for duplicates before saving
                    if not await event_exists(event):
                        await save_economic_event(event)
                        saved_count += 1
                    else:
                        logger.debug(f"Event already exists: {event['title']}")
                except Exception as e:
                    logger.error(f"Error processing event: {e}")
                    continue

            logger.info(f"Saved {saved_count} new economic events out of {len(events)}")
        else:
            logger.warning("No economic events found from Forex Factory API")

    except Exception as e:
        logger.error(f"Error in economic calendar collection: {e}", exc_info=True)

    logger.info("Economic calendar collection completed")

async def collect_events_for_greeting():
    """Collect events specifically for greeting message generation"""
    logger.info("Collecting events for greeting message")

    try:
        # Always fetch fresh data for greeting
        events = await fetch_events_from_forex_factory()

        if events:
            # Save events to database
            saved_count = 0
            for event in events:
                try:
                    if not await event_exists(event):
                        await save_economic_event(event)
                        saved_count += 1
                except Exception as e:
                    logger.error(f"Error saving event for greeting: {e}")
                    continue

            logger.info(f"Saved {saved_count} new events for greeting")
            return events
        else:
            logger.warning("No events found for greeting")
            return []

    except Exception as e:
        logger.error(f"Error collecting events for greeting: {e}", exc_info=True)
        return []

async def fetch_events_from_forex_factory():
    """
    Fetch economic calendar events from Forex Factory API with retry logic
    and fallback to alternative endpoints if needed

    Returns:
        list: List of economic events for today
    """
    events = []

    # Try each endpoint until we get a valid response
    for endpoint_index, endpoint_url in enumerate(ALTERNATIVE_ENDPOINTS):
        try:
            logger.info(f"Fetching events from endpoint {endpoint_index + 1}/{len(ALTERNATIVE_ENDPOINTS)}: {endpoint_url}")

            # Make request to Forex Factory API with retry logic
            max_retries = 5  # Increased retries for better reliability
            response = None
            success = False

            for attempt in range(max_retries):
                try:
                    # Add headers to avoid rate limiting with rotating user agents
                    headers = {
                        "User-Agent": random.choice(USER_AGENTS),
                        "Accept": "application/json, text/plain, */*",
                        "Accept-Language": "en-US,en;q=0.9",
                        "Cache-Control": "no-cache",
                        "Pragma": "no-cache",
                        "Referer": "https://www.forexfactory.com/",
                        "Origin": "https://www.forexfactory.com"
                    }

                    # Configure timeout with separate connect and read timeouts
                    timeout = httpx.Timeout(
                        connect=CONNECT_TIMEOUT,
                        read=READ_TIMEOUT,
                        write=10.0,
                        pool=5.0
                    )

                    # Configure connection limits for better reliability
                    limits = httpx.Limits(
                        max_connections=5,
                        max_keepalive_connections=2
                    )

                    async with httpx.AsyncClient(
                        timeout=timeout,
                        limits=limits,
                        follow_redirects=True
                    ) as client:
                        response = await client.get(endpoint_url, headers=headers)

                        if response.status_code == 429:  # Rate limited
                            delay = 10 * (2 ** attempt)  # Exponential backoff starting at 10 seconds
                            logger.warning(f"Rate limited (429). Waiting {delay} seconds before retry...")
                            await asyncio.sleep(delay)
                            continue
                        elif response.status_code == 403:
                            logger.warning(f"Access forbidden (403) for endpoint {endpoint_url} - likely blocked or requires authentication")
                            logger.debug(f"Response content: {response.text[:200]}")
                            # For 403 errors, skip to next endpoint immediately as retrying won't help
                            break  # Try next endpoint
                        elif response.status_code != 200:
                            logger.error(f"Failed to fetch economic calendar: HTTP {response.status_code}")
                            logger.debug(f"Response content: {response.text[:200]}")

                            if attempt == max_retries - 1:
                                break  # Try next endpoint
                            else:
                                await asyncio.sleep(5 * (attempt + 1))  # Longer delay for other errors
                                continue

                        # Success, break out of retry loop
                        success = True
                        break

                except (httpx.RequestError, httpx.TimeoutException, httpx.ConnectError, httpx.ReadError) as e:
                    error_message = str(e).lower()
                    error_type = type(e).__name__
                    logger.warning(f"Attempt {attempt + 1} failed for endpoint {endpoint_url}: {error_type}: {e}")

                    # Special handling for different error types
                    if "temporary failure in name resolution" in error_message:
                        logger.warning("🌐 DNS resolution failure - network connectivity issue")
                        logger.warning("💡 This usually indicates internet connectivity problems or DNS server issues")
                        backoff_delay = 10 * (2 ** attempt)  # Longer backoff for DNS issues
                    elif "read error" in error_message or "ReadError" in error_type:
                        logger.warning("📖 Read error - connection interrupted during data transfer")
                        backoff_delay = 5 * (2 ** attempt)  # Moderate backoff for read errors
                    elif "connect" in error_message or "ConnectError" in error_type:
                        logger.warning("🔌 Connection error - unable to establish connection")
                        backoff_delay = 3 * (2 ** attempt)  # Standard backoff for connection errors
                    elif "timeout" in error_message or "TimeoutException" in error_type:
                        logger.warning("⏰ Timeout error - request took too long")
                        backoff_delay = 2 * (2 ** attempt)  # Shorter backoff for timeouts
                    else:
                        logger.warning(f"🌐 Network error: {error_type}")
                        backoff_delay = 2 ** attempt  # Standard exponential backoff

                    if attempt == max_retries - 1:
                        logger.error(f"All {max_retries} attempts failed for endpoint {endpoint_url}")
                        logger.error(f"Last error: {error_type}: {e}")
                        break  # Try next endpoint

                    # Apply calculated backoff delay with maximum cap
                    backoff_delay = min(backoff_delay, 300)  # Cap at 5 minutes
                    logger.info(f"⏳ Waiting {backoff_delay} seconds before retry...")
                    await asyncio.sleep(backoff_delay)

                except Exception as e:
                    logger.error(f"Unexpected error on attempt {attempt + 1} for endpoint {endpoint_url}: {e}")
                    if attempt == max_retries - 1:
                        logger.error(f"All {max_retries} attempts failed for endpoint {endpoint_url}")
                        break  # Try next endpoint
                    await asyncio.sleep(2 ** attempt)

            # If all retries failed, try next endpoint
            if not success or not response:
                logger.warning(f"All attempts failed for endpoint {endpoint_url}, trying next endpoint if available")
                continue

            # Parse JSON response with error handling
            try:
                # Check if response has content
                if not response.text.strip():
                    logger.warning(f"Empty response from endpoint {endpoint_url}")
                    continue  # Try next endpoint

                # Check if response looks like HTML (common when API returns error pages)
                response_text = response.text.strip()
                if response_text.startswith('<') and response_text.endswith('>'):
                    logger.warning(f"Received HTML response instead of JSON from endpoint {endpoint_url}")

                    # If this is the fallback endpoint (forexfactory.com), we expect HTML
                    if "forexfactory.com" in endpoint_url:
                        logger.info("Attempting to parse HTML from ForexFactory website")
                        html_events = await parse_forex_factory_html(response_text)
                        if html_events:
                            return html_events

                    # For other endpoints, try the next one
                    continue

                # Check if response looks like JSON
                if not (response_text.startswith('{') or response_text.startswith('[')):
                    logger.warning(f"Response doesn't look like JSON from endpoint {endpoint_url}")
                    continue  # Try next endpoint

                api_events = response.json()

                if not api_events:
                    logger.warning(f"No events found in API response from endpoint {endpoint_url}")
                    continue  # Try next endpoint

                # Successfully parsed JSON, process events
                logger.info(f"Received {len(api_events)} events from endpoint {endpoint_url}")

                # Filter events for today only
                today = datetime.now(timezone.utc).date()
                today_events = []

                for event in api_events:
                    try:
                        # Parse the event date and ensure it's timezone-aware (UTC)
                        event_date = datetime.fromisoformat(event['date'].replace('Z', '+00:00'))

                        # Ensure the event_date is in UTC timezone
                        if event_date.tzinfo is None:
                            event_date = event_date.replace(tzinfo=timezone.utc)
                        else:
                            event_date = event_date.astimezone(timezone.utc)

                        # Check if event is today (in UTC)
                        if event_date.date() == today:
                            # Convert impact to numeric value
                            impact_map = {"Low": 1, "Medium": 2, "High": 3}
                            impact = impact_map.get(event.get('impact', 'Low'), 1)

                            # Map currency codes
                            currency_map = {
                                "USD": "USD", "EUR": "EUR", "GBP": "GBP", "JPY": "JPY",
                                "AUD": "AUD", "CAD": "CAD", "CHF": "CHF", "NZD": "NZD",
                                "CNY": "CNY"
                            }
                            currency = currency_map.get(event.get('country', ''), event.get('country', ''))

                            processed_event = {
                                'title': event['title'],
                                'country': event.get('country', ''),
                                'currency': currency,
                                'impact': impact,
                                'event_time': event_date,
                                'previous': event.get('previous', ''),
                                'forecast': event.get('forecast', ''),
                                'actual': event.get('actual', '')
                            }

                            today_events.append(processed_event)

                    except Exception as e:
                        logger.error(f"Error processing event {event}: {e}")
                        continue

                logger.info(f"Filtered {len(today_events)} events for today")
                return today_events

            except ValueError as e:
                logger.warning(f"Invalid JSON response from endpoint {endpoint_url}: {e}")
                logger.debug(f"Response content: {response.text[:500]}")
                # Check if this might be a temporary API issue
                if "Expecting value: line 1 column 1 (char 0)" in str(e):
                    logger.warning("This appears to be an empty or malformed response - likely temporary API issue")
                continue  # Try next endpoint
            except Exception as e:
                logger.warning(f"Unexpected error parsing response from endpoint {endpoint_url}: {e}")
                logger.debug(f"Response content: {response.text[:500]}")
                continue  # Try next endpoint

        except Exception as e:
            logger.warning(f"Error fetching economic calendar from endpoint {endpoint_url}: {e}")
            continue  # Try next endpoint

    # If we've tried all endpoints and none worked
    logger.error("❌ All endpoints failed for economic calendar data")
    logger.error("💡 This could be due to:")
    logger.error("   - Network connectivity issues")
    logger.error("   - API endpoints being temporarily unavailable")
    logger.error("   - Rate limiting or IP blocking")
    logger.error("   - DNS resolution problems")
    logger.warning("📊 Economic calendar data will be unavailable until connectivity is restored")
    return events


async def parse_forex_factory_html(html_content):
    """
    Parse economic events from Forex Factory HTML content
    This is a fallback method when the JSON API fails

    Args:
        html_content (str): HTML content from Forex Factory website

    Returns:
        list: List of economic events in the same format as the API
    """
    events = []

    try:
        soup = BeautifulSoup(html_content, 'html.parser')

        # Find the calendar table
        calendar_table = soup.find('table', class_='calendar__table')
        if not calendar_table:
            logger.error("Could not find calendar table in Forex Factory HTML")
            return events

        # Process each event row
        event_rows = calendar_table.find_all('tr', class_='calendar__row')

        for row in event_rows:
            try:
                # Skip header rows
                if row.get('data-eventid') is None:
                    continue

                # Extract event data
                event_data = {}

                # Get date
                date_cell = row.find('td', class_='calendar__cell--date')
                if date_cell:
                    date_str = date_cell.get('data-date', '')
                    if date_str:
                        # Convert to ISO format
                        try:
                            # Format is typically YYYY-MM-DD
                            date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                            # Add time if available
                            time_cell = row.find('td', class_='calendar__cell--time')
                            if time_cell and time_cell.text.strip():
                                time_str = time_cell.text.strip()
                                # Try to parse time (format varies)
                                try:
                                    # Common format: "8:30am"
                                    if re.match(r'\d+:\d+[ap]m', time_str):
                                        time_obj = datetime.strptime(time_str, '%I:%M%p')
                                        date_obj = date_obj.replace(
                                            hour=time_obj.hour,
                                            minute=time_obj.minute
                                        )
                                except Exception as e:
                                    logger.debug(f"Could not parse time: {time_str}, {e}")

                            # Set timezone to UTC (approximation)
                            date_obj = date_obj.replace(tzinfo=timezone.utc)
                            event_data['date'] = date_obj.isoformat()
                        except Exception as e:
                            logger.debug(f"Error parsing date: {date_str}, {e}")
                            continue

                # Get title
                title_cell = row.find('td', class_='calendar__cell--event')
                if title_cell:
                    event_title = title_cell.find('span', class_='calendar__event-title')
                    if event_title:
                        event_data['title'] = event_title.text.strip()

                # Get country/currency
                currency_cell = row.find('td', class_='calendar__cell--currency')
                if currency_cell:
                    event_data['country'] = currency_cell.text.strip()

                # Get impact
                impact_cell = row.find('td', class_='calendar__cell--impact')
                if impact_cell:
                    impact_spans = impact_cell.find_all('span', class_='calendar__impact-icon')
                    if len(impact_spans) == 3:
                        event_data['impact'] = 'High'
                    elif len(impact_spans) == 2:
                        event_data['impact'] = 'Medium'
                    elif len(impact_spans) == 1:
                        event_data['impact'] = 'Low'
                    else:
                        event_data['impact'] = 'Holiday'  # Default for non-standard impacts

                # Get forecast
                forecast_cell = row.find('td', class_='calendar__cell--forecast')
                if forecast_cell:
                    event_data['forecast'] = forecast_cell.text.strip()

                # Get previous
                previous_cell = row.find('td', class_='calendar__cell--previous')
                if previous_cell:
                    event_data['previous'] = previous_cell.text.strip()

                # Get actual if available
                actual_cell = row.find('td', class_='calendar__cell--actual')
                if actual_cell:
                    event_data['actual'] = actual_cell.text.strip()

                # Add to events list if we have the minimum required data
                if 'title' in event_data and 'date' in event_data:
                    events.append(event_data)

            except Exception as e:
                logger.error(f"Error parsing event row: {e}")
                continue

        logger.info(f"Parsed {len(events)} events from Forex Factory HTML")
        return events

    except Exception as e:
        logger.error(f"Error parsing Forex Factory HTML: {e}")
        return events


async def event_exists(event_data):
    """
    Check if an economic event already exists in the database

    Args:
        event_data (dict): Economic event data

    Returns:
        bool: True if event exists, False otherwise
    """
    async for db in get_async_db():
        try:
            if is_sqlite_db():
                result = db.execute(
                    select(EconomicEvent).where(
                        EconomicEvent.title == event_data["title"],
                        EconomicEvent.country == event_data.get("country"),
                        EconomicEvent.event_time == event_data["event_time"]
                    )
                )
            else:
                result = await db.execute(
                    select(EconomicEvent).where(
                        EconomicEvent.title == event_data["title"],
                        EconomicEvent.country == event_data.get("country"),
                        EconomicEvent.event_time == event_data["event_time"]
                    )
                )
            existing_event = result.scalars().first()
            return existing_event is not None
        except Exception as e:
            logger.error(f"Error checking if event exists: {e}")
            return False

async def save_economic_event(event_data):
    """
    Save economic event to database

    Args:
        event_data (dict): Economic event data
    """
    async for db in get_async_db():
        try:
            # Ensure event_time is timezone-aware (UTC)
            event_time = event_data["event_time"]
            if event_time.tzinfo is None:
                event_time = event_time.replace(tzinfo=timezone.utc)
            else:
                event_time = event_time.astimezone(timezone.utc)

            # Create new economic event
            economic_event = EconomicEvent(
                title=event_data["title"],
                country=event_data.get("country"),
                impact=event_data.get("impact"),
                previous=event_data.get("previous"),
                forecast=event_data.get("forecast"),
                actual=event_data.get("actual"),
                event_time=event_time,
                currency=event_data.get("currency"),
                created_at=datetime.now(timezone.utc)
            )

            db.add(economic_event)
            if is_sqlite_db():
                db.commit()
            else:
                await db.commit()

            logger.info(f"Saved new economic event: {event_data['title']}")

        except Exception as e:
            if is_sqlite_db():
                db.rollback()
            else:
                await db.rollback()
            logger.error(f"Error saving economic event: {e}", exc_info=True)
            raise
