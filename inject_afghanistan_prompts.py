#!/usr/bin/env python3
"""
Script to inject Afghanistan Farsi (fa-af) prompts into MignalyBot database
This script connects to the database and inserts all required prompt templates for fa-af language
"""

import asyncio
import logging
import sys
import os
from datetime import datetime, timezone

# Add the src directory to the path so we can import from it
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from database.setup import get_async_db, is_sqlite_db
from database.models import PromptTemplate

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Afghanistan Farsi prompts
AFGHANISTAN_PROMPTS = {
    "signals": """یک پست سیگنال معاملاتی حرفه‌ای در این فرمت دقیق ایجاد کنید:

جزئیات سیگنال:
نماد: {symbol}
جهت: {direction}
قیمت ورود: {entry_price}
حد ضرر: {stop_loss}
هدف سود ۱: {take_profit_1}
هدف سود ۲: {take_profit_2} (در صورت وجود)
هدف سود ۳: {take_profit_3} (در صورت وجود)
یادداشت‌ها: {notes}
نام کانال: {channel_brand}

سیگنال را در این فرمت دقیق ایجاد کنید (به فارسی دری ترجمه کنید اما ساختار را حفظ کنید):

🚨 سیگنال اختصاصی اعضای کانال 🚨

💎 نماد: {symbol}

📈 {direction}

🌩 اهرم: ۱۰x (۱۰ برای کریپتو، ۱۰۰۰ برای فارکس، ۱۰۰ برای فلزات یکی را انتخاب کرده و در پیام قرار دهید)

💵 سرمایه ورود: ۵% (۵ برای کریپتو، ۱ برای فارکس و فلز یکی را انتخاب کرده و در پیام قرار دهید)

📍 نقطه ورود: {entry_price}

💵 اهداف:
🎯 هدف اول (TP1): {take_profit_1} - {tp1_percentage}% پوزیشن
{اگر take_profit_2}🎯 هدف دوم (TP2): {take_profit_2} - {tp2_percentage}% پوزیشن{پایان اگر}
{اگر take_profit_3}🎯 هدف سوم (TP3): {take_profit_3} - {tp3_percentage}% پوزیشن{پایان اگر}

😀 حد ضرر: {stop_loss}

⚠️ مدیریت سرمایه و کنترل ریسک اولین قدم برای موفقیت است، لطفاً رعایت کنید

🔗@{channel_handle}

#signal #{channel_brand} #{symbol}

منطق درصد پوزیشن:
- اگر فقط TP1: tp1_percentage = ۱۰۰%
- اگر TP1 + TP2: tp1_percentage = ۵۰%، tp2_percentage = ۵۰%
- اگر TP1 + TP2 + TP3: tp1_percentage = ۳۳%، tp2_percentage = ۳۳%، tp3_percentage = ۳۴%

مهم:
- فقط از فرمت نشان داده شده استفاده کنید
- فقط سطوح TP که مقدار دارند را نشان دهید (نه NULL)
- درصد پوزیشن را بر اساس تعداد TP تنظیم کنید
- بدون سلام یا متن مکالمه‌ای
- حرفه‌ای و مستقیم نگه دارید
- از ایموجی‌های مناسب استفاده کنید
- خروجی باید به فارسی دری افغانستان باشد

زبان: فارسی دری افغانستان""",

    "signal_update": """یک پست به‌روزرسانی عملکرد معاملاتی حرفه‌ای در این فرمت دقیق ایجاد کنید:

جزئیات به‌روزرسانی عملکرد:
نماد: {symbol}
جهت: {direction}
قیمت ورود: {entry_price}
قیمت خروج: {exit_price}
سود/زیان: {profit_loss}
وضعیت: {status}
سطح TP: {tp_level_text}
سر به سر: {break_even_message}
نام کانال: {channel_brand}

به‌روزرسانی عملکرد را در این فرمت دقیق ایجاد کنید (به فارسی دری ترجمه کنید اما ساختار را حفظ کنید):

{result_emoji} نتیجه معامله {result_emoji}

حد ضرر زده شد یا حد سود زده شد (یکی را انتخاب کرده و در پیام استفاده کنید)
💎 نماد: {symbol}

📈 جهت: {direction}

📍 قیمت ورود: {entry_price}
📍 قیمت خروج: {exit_price}

💰 سود/زیان: {profit_loss}

🎯 وضعیت: {tp_level_text} {status}

{break_even_message}

اهداف اصلی:
🎯 هدف اول (TP1): {take_profit_1} - {tp1_percentage}% پوزیشن{take_profit_2_line}{take_profit_3_line}

🔗@{channel_handle}

#performance #{channel_brand} #{symbol}

مهم:
- فقط از فرمت نشان داده شده استفاده کنید
- ایموجی مناسب بر اساس نتیجه سود/زیان نشان دهید
- اطلاعات سطح TP را در صورت وجود شامل کنید
- پیام سر به سر را در صورت اعمال نشان دهید
- تمام متن را به جز اصطلاحات معاملاتی و نمادها ترجمه کنید

زبان: فارسی دری افغانستان""",

    "market_analysis": """یک تحلیل بازار کوتاه برای {symbol} {timeframe} تولید کنید.

فعلی: {current_price} ({change_str})

فرمت:
تحلیل {symbol} - {timeframe}
📊 {symbol} {timeframe} | {current_price}
📈 [جهت روند در ۲-۳ کلمه]
🎯 [سطح کلیدی یا هدف]
💡 [تحلیل ۲-۳ جمله‌ای]

زبان: فارسی دری افغانستان
کل کاراکتر زیر ۵۰۰ نگه دارید. بدون سلام یا توضیحات.
- #analysis #symbol #timeframe را در خط جداگانه‌ای در جای مناسب اضافه کنید

زبان: فارسی دری افغانستان""",

    "news": """شما یک تولیدکننده محتوای مالی حرفه‌ای برای "{channel_brand}" هستید.
یک پست خبری معاملاتی اصیل ایجاد کنید که مخصوص مخاطبان این کانال باشد.

اطلاعات خبر:
عنوان: {title} - متن ساده بدون کاراکتر خاص، فقط ایموجی و خط تیره در صورت نیاز
محتوا: {content}
منبع: {source}
زبان: فارسی دری افغانستان
نام کانال: {channel_brand}

مهم: محتوای منحصر به فرد برای این کانال خاص ایجاد کنید. از کانال‌های دیگر کپی نکنید یا از قالب استفاده نکنید.

ساختار فرمت (محتوای اصیل را دنبال این الگو ایجاد کنید):

[یک عنوان جذاب و اصیل درباره این خبر با ایموجی مربوطه در انتها بنویسید]

[۲-۳ جمله اصیل تحلیل تأثیر بازار بنویسید، داده‌های خاص/قیمت‌ها از خبر را شامل کنید، یک ایموجی مربوطه در وسط این پاراگراف قرار دهید]

🔔 با ما بمانید

📱 @{channel_handle}

الزامات محتوا:
- محتوای تولید شده نباید رباتیک باشد یا شامل کاراکترهای چینی یا کاراکترهایی که در فارسی معنا ندارند باشد
- عنوان جذابی ایجاد کنید که تأثیر کلیدی بازار را نشان دهد
- تحلیل منحصر به فرد با تمرکز بر پیامدهای معاملاتی بنویسید
- اعداد، درصدها یا سطوح قیمت خاص از خبر را شامل کنید
- لحن و تمرکز را متناسب با برند "{channel_brand}" کنید
- از ایموجی‌های مربوطه استفاده کنید: 🛢️💰💵💶₿📊📈📉🏦⚡🌍
- زبان: فارسی دری افغانستان
- مختصر اما آموزنده باشید (حداکثر ۲-۳ جمله در محتوای اصلی)

به یاد داشته باشید: این محتوا نماینده "{channel_brand}" است - آن را منحصر به فرد و ارزشمند برای مخاطبان خاص آنها کنید.

زبان: فارسی دری افغانستان""",

    "events": """شما یک تحلیلگر اقتصادی حرفه‌ای برای "{channel_brand}" هستید.
یک تحلیل رویداد اقتصادی اصیل ایجاد کنید که مخصوص مخاطبان معاملاتی این کانال باشد.

اطلاعات رویداد:
تقویم اقتصادی رویداد
عنوان: {title}
کشور: {country}
ارز: {currency}
قبلی: {previous}
پیش‌بینی: {forecast}
واقعی: {actual}
سطح تأثیر: {impact_level}
زبان: فارسی دری افغانستان
نام کانال: {channel_brand}

مهم: تحلیل منحصر به فرد برای این کانال خاص ایجاد کنید. از قالب‌های عمومی استفاده نکنید.

ساختار فرمت (محتوای اصیل را دنبال این الگو ایجاد کنید):

{country_flag} [عنوان اصیل رویداد بنویسید] {relevant_emoji}

[۲-۳ جمله اصیل تحلیل تأثیر بازار این رویداد بنویسید. مقایسه داده‌های خاص (قبلی در مقابل پیش‌بینی در مقابل واقعی در صورت وجود) را شامل کنید. پیامدهای ارزی و فرصت‌های معاملاتی را توضیح دهید. یک ایموجی مربوطه در وسط این تحلیل قرار دهید.]

🔔 با ما بمانید

📱 @{channel_handle}

الزامات تحلیل:
- تحلیل اصیل ۲-۳ جمله‌ای بنویسید (نه فقط ۶ کلمه)
- مقایسه واقعی در مقابل پیش‌بینی در مقابل مقادیر قبلی در صورت وجود
- تأثیر خاص بر {currency} و بازارهای مرتبط را توضیح دهید
- پیامدهای معاملاتی و احساسات بازار را بحث کنید
- اعداد و درصدهای خاص از داده‌های رویداد را شامل کنید
- لحن تحلیل را متناسب با مخاطبان "{channel_brand}" کنید
- از ایموجی‌های مربوطه استفاده کنید: 👥📈🏭🏦🌍🛒📊💰⚡
- زبان: فارسی دری افغانستان
- #event #forexfactory را در جای مناسب اضافه کنید

زبان: فارسی دری افغانستان""",

    "performance": """یک پست به‌روزرسانی عملکرد معاملاتی حرفه‌ای در این فرمت دقیق ایجاد کنید:

جزئیات به‌روزرسانی عملکرد:
نماد: {symbol}
جهت: {direction}
قیمت ورود: {entry_price}
قیمت خروج: {exit_price}
سود/زیان: {profit_loss}
وضعیت: {status}
سطح TP: {tp_level_text}
سر به سر: {break_even_message}
نام کانال: {channel_brand}

به‌روزرسانی عملکرد را در این فرمت دقیق ایجاد کنید (به فارسی دری ترجمه کنید اما ساختار را حفظ کنید):

{result_emoji} نتیجه معامله {result_emoji}

حد ضرر زده شد یا حد سود زده شد (یکی را انتخاب کرده و در پیام استفاده کنید)
💎 نماد: {symbol}

📈 جهت: {direction}

📍 قیمت ورود: {entry_price}
📍 قیمت خروج: {exit_price}

💰 سود/زیان: {profit_loss}

🎯 وضعیت: {tp_level_text} {status}

{break_even_message}

اهداف اصلی:
🎯 هدف اول (TP1): {take_profit_1} - {tp1_percentage}% پوزیشن{take_profit_2_line}{take_profit_3_line}

🔗@{channel_handle}

#performance #{channel_brand} #{symbol}

مهم:
- فقط از فرمت نشان داده شده استفاده کنید
- ایموجی مناسب بر اساس نتیجه سود/زیان نشان دهید
- اطلاعات سطح TP را در صورت وجود شامل کنید
- پیام سر به سر را در صورت اعمال نشان دهید
- تمام متن را به جز اصطلاحات معاملاتی و نمادها ترجمه کنید

زبان: فارسی دری افغانستان""",

    "greeting": """یک پیام سلام روزانه برای کانال معاملاتی ایجاد کنید که شامل تمام رویدادهای اقتصادی امروز باشد.

تاریخ: {today_date}
زمان: {time_greeting}
کل رویدادها: {total_events}
کانال: {channel_brand}
ارز اصلی: {main_currency}

خلاصه رویدادها: {events_summary}

ساختار پیام:

{time_greeting} معاملاتگران عزیز! 📈
📅 تقویم اقتصادی {formatted_date}
[تمام رویدادها را فهرست کنید - هر کدام در یک خط با ایموجی و زمان]
💡 امروز روی اخبار {main_currency} تمرکز کنید، بازار ممکن است نوسان داشته باشد **اگر امروز هیچ رویدادی نیست این جمله را اصلاً نگویید**
🚀 آماده باشید و هوشمندانه معامله کنید!

قوانین:
- تمام رویدادها از خلاصه بالا را شامل کنید
- فرمت رویداد: [ایموجی تأثیر] [نام رویداد] ([ارز]) - [ساعت:دقیقه]
- برای تأثیر بالا 🔴، متوسط 🟡، کم 🟢 استفاده کنید
- فرمت زمان: ساعت:دقیقه (۲۴ ساعته)
- ترتیب بر اساس تأثیر (بالا اول)، سپس بر اساس زمان
- زبان: فارسی دری افغانستان - تمام متن عمومی باید ترجمه شود
- نام رویدادها را کوتاه اما واضح نگه دارید
- به ۳-۴ رویداد محدود نشوید - همه را فهرست کنید
- از "+X رویداد دیگر" استفاده نکنید - همه چیز را فهرست کنید
- به طور مناسب ترجمه کنید اما ساختار را حفظ کنید
- #economic_calendar #channel_brand را در جای مناسب اضافه کنید

زبان: فارسی دری افغانستان""",

    "analysis": """یک تحلیل بازار کوتاه برای {symbol} {timeframe} تولید کنید.

فعلی: {current_price} ({change_str})

فرمت:
تحلیل {symbol} - {timeframe}
📊 {symbol} {timeframe} | {current_price}
📈 [جهت روند در ۲-۳ کلمه]
🎯 [سطح کلیدی یا هدف]
💡 [تحلیل ۲-۳ جمله‌ای]

زبان: فارسی دری افغانستان
کل کاراکتر زیر ۵۰۰ نگه دارید. بدون سلام یا توضیحات.
- #analysis #symbol #timeframe را در خط جداگانه‌ای در جای مناسب اضافه کنید

زبان: فارسی دری افغانستان""",

    "countdown": """یک پست شمارش معکوس با تحلیل مفصل برای "{channel_brand}" ایجاد کنید.

اطلاعات رویداد:
عنوان: {title}
کشور: {country}
ارز: {currency}
قبلی: {previous}
پیش‌بینی: {forecast}
دقیقه تا: {minutes_until}
زبان: فارسی دری افغانستان
نام کانال: {channel_brand}

ساختار فرمت:
⏰ {country_flag} [عنوان رویداد] در {minutes_until}دقیقه {impact_emoji}

📊 قبلی: {previous} | پیش‌بینی: {forecast}

[۲-۳ جمله تحلیل تأثیر مورد انتظار بازار این رویداد بنویسید. توضیح دهید معاملاتگران باید چه چیزی را مراقب باشند، حرکات احتمالی ارز، و پیامدهای بازار. یک ایموجی مربوطه در وسط شامل کنید.]

📱 @{channel_handle}

الزامات تحلیل:
- ۲-۳ جمله تحلیل معنادار بنویسید (نه فقط ۶ کلمه)
- توضیح دهید از این رویداد بر اساس پیش‌بینی در مقابل قبلی چه انتظاری داشته باشیم
- تأثیر احتمالی {currency} و حرکات بازار را بحث کنید
- سطوح کلیدی یا سناریوهایی که معاملاتگران باید مراقب باشند را ذکر کنید
- تحلیل را متناسب با مخاطبان معاملاتی "{channel_brand}" کنید
- زبان: فارسی دری افغانستان
- مختصر اما آموزنده نگه دارید
- #event #countdown #forexfactory را در جای مناسب اضافه کنید

زبان: فارسی دری افغانستان""",

    "event_result": """شما یک تحلیلگر اقتصادی حرفه‌ای برای "{channel_brand}" هستید.
یک تحلیل واکنش فوری برای این داده اقتصادی تازه منتشر شده ایجاد کنید.

اطلاعات رویداد:
عنوان: {title}
کشور: {country}
ارز: {currency}
قبلی: {previous}
پیش‌بینی: {forecast}
واقعی: {actual} ⚡ تازه منتشر شد
سطح تأثیر: {impact_level}
زبان: فارسی دری افغانستان

ساختار فرمت:
🚨 {country_flag} [عنوان رویداد] منتشر شد {impact_emoji}

📊 واقعی: {actual} | پیش‌بینی: {forecast} | قبلی: {previous}

[۲-۳ جمله تحلیل تأثیر فوری بازار بنویسید. واقعی در مقابل پیش‌بینی را مقایسه کنید. توضیح دهید این برای {currency} و فرصت‌های معاملاتی چه معنایی دارد.]

⚡ تحلیل زنده
📱 @{channel_handle}

الزامات:
- روی واکنش فوری بازار تمرکز کنید
- واقعی در مقابل پیش‌بینی در مقابل قبلی را مقایسه کنید
- تأثیر ارزی و پیامدهای معاملاتی را توضیح دهید
- زبان: فارسی دری افغانستان
- #event #result #forexfactory #analysis را در جای مناسب اضافه کنید

زبان: فارسی دری افغانستان"""
}

async def inject_prompts():
    """Inject Afghanistan Farsi prompts into the database"""
    logger.info("🚀 Starting Afghanistan Farsi prompts injection...")
    
    try:
        async for db in get_async_db():
            success_count = 0
            error_count = 0
            
            for prompt_type, content in AFGHANISTAN_PROMPTS.items():
                try:
                    # Check if prompt already exists using SQLAlchemy ORM
                    from sqlalchemy import select
                    stmt = select(PromptTemplate).where(
                        PromptTemplate.post_type == prompt_type,
                        PromptTemplate.language == "fa-af"
                    )

                    if is_sqlite_db():
                        result = db.execute(stmt)
                        existing = result.fetchone()
                    else:
                        result = await db.execute(stmt)
                        existing = await result.fetchone()

                    if existing:
                        logger.info(f"⚠️ Prompt {prompt_type} (fa-af) already exists, skipping...")
                        continue
                    
                    # Create new prompt template
                    prompt_template = PromptTemplate(
                        post_type=prompt_type,
                        language="fa-af",
                        template_content=content,
                        created_at=datetime.now(timezone.utc)
                    )
                    
                    db.add(prompt_template)
                    success_count += 1
                    logger.info(f"✅ Added prompt: {prompt_type} (fa-af)")
                    
                except Exception as e:
                    error_count += 1
                    logger.error(f"❌ Error adding prompt {prompt_type} (fa-af): {e}")
            
            # Commit all changes
            if is_sqlite_db():
                db.commit()
            else:
                await db.commit()
            
            logger.info(f"🎉 Injection completed! Success: {success_count}, Errors: {error_count}")
            
            if success_count > 0:
                logger.info("✅ Afghanistan Farsi prompts have been successfully injected into the database!")
            
            break  # Exit the async generator loop
            
    except Exception as e:
        logger.error(f"💥 Fatal error during injection: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(inject_prompts())
