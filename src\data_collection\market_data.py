"""
Market data collection module for MignalyBot
Uses MetaTrader 5 API for market data
"""

import logging
import asyncio
import httpx
import json
from datetime import datetime, timedelta, timezone
import pandas as pd
from sqlalchemy import select

from src.database.setup import get_async_db
from src.database.models import CandleData
from src.utils.network_utils import create_http_client, retry_with_backoff, NetworkError

logger = logging.getLogger(__name__)

# MetaTrader 5 API endpoints
MT5_API_BASE = "http://154.53.166.53:8000/api/market"
MT5_SYMBOLS_URL = f"{MT5_API_BASE}/symbols"
MT5_CANDLES_URL = f"{MT5_API_BASE}/candles"
MT5_JOB_STATUS_URL = f"{MT5_API_BASE}/job-status"

# Supported timeframes mapping
TIMEFRAME_MAP = {
    # Standard format (preferred)
    "1m": "M1",
    "5m": "M5",
    "15m": "M15",
    "30m": "M30",
    "1h": "H1",
    "4h": "H4",
    "1d": "D1",
    "1w": "W1",
    "1M": "MN1",  # Monthly

    # Alternative formats for backward compatibility
    "M1": "M1",
    "M5": "M5",
    "M15": "M15",
    "M30": "M30",
    "H1": "H1",
    "H4": "H4",
    "D1": "D1",
    "W1": "W1",
    "MN1": "MN1",
}

# Cache for available symbols (refresh monthly)
_symbols_cache = None
_symbols_cache_time = None

async def get_available_symbols():
    """
    Get available symbols from MT5 API with caching

    Returns:
        list: List of available symbols
    """
    global _symbols_cache, _symbols_cache_time

    # Check if cache is valid (refresh monthly)
    now = datetime.now(timezone.utc)
    if (_symbols_cache is not None and _symbols_cache_time is not None and
        now - _symbols_cache_time < timedelta(days=30)):
        logger.info(f"Using cached symbols ({len(_symbols_cache)} symbols)")
        return _symbols_cache

    logger.info("Fetching available symbols from MT5 API")

    try:
        # Request symbols
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(MT5_SYMBOLS_URL)

            if response.status_code != 200:
                logger.error(f"Failed to fetch symbols: {response.status_code}")
                logger.debug(f"Response content: {response.text[:200]}")
                return _symbols_cache or []

            # Parse JSON response with error handling
            try:
                if not response.text.strip():
                    logger.error("Empty response from MT5 symbols API")
                    return _symbols_cache or []
                data = response.json()
            except ValueError as e:
                logger.error(f"Invalid JSON response from MT5 symbols API: {e}")
                logger.debug(f"Response content: {response.text[:500]}")
                return _symbols_cache or []
            job_id = data.get("job_id")

            if not job_id:
                logger.error("No job_id received from symbols request")
                return _symbols_cache or []

            # Wait for job completion
            await asyncio.sleep(30)

            # Get job result
            job_response = await client.post(
                MT5_JOB_STATUS_URL,
                json={"job_id": job_id}
            )

            if job_response.status_code != 200:
                logger.error(f"Failed to get job status: {job_response.status_code}")
                logger.debug(f"Response content: {job_response.text[:200]}")
                return _symbols_cache or []

            # Parse JSON response with error handling
            try:
                if not job_response.text.strip():
                    logger.error("Empty response from MT5 job status API")
                    return _symbols_cache or []
                job_data = job_response.json()
            except ValueError as e:
                logger.error(f"Invalid JSON response from MT5 job status API: {e}")
                logger.debug(f"Response content: {job_response.text[:500]}")
                return _symbols_cache or []

            if job_data.get("status") != "finished":
                logger.error(f"Job not finished: {job_data.get('status')}")
                return _symbols_cache or []

            result = job_data.get("result", {})
            symbols = result.get("symbols", [])

            # Cache the symbols
            _symbols_cache = symbols
            _symbols_cache_time = now

            logger.info(f"Fetched {len(symbols)} symbols from MT5 API")
            return symbols

    except Exception as e:
        logger.error(f"Error fetching symbols: {e}", exc_info=True)
        return _symbols_cache or []

async def collect_market_data(symbols, timeframes):
    """
    Collect market data for specified symbols and timeframes using MT5 API

    Args:
        symbols (list): List of symbols to collect data for
        timeframes (list): List of timeframes to collect data for
    """
    logger.info(f"Collecting market data for {len(symbols)} symbols and {len(timeframes)} timeframes")

    # Skip symbol availability check for now since MT5 API is working
    # We'll directly try to fetch data for the requested symbols
    logger.info("Bypassing symbol availability check - directly fetching data")

    for symbol in symbols:
        for timeframe in timeframes:
            try:
                # Skip unsupported timeframes
                if timeframe not in TIMEFRAME_MAP:
                    logger.error(f"Unsupported timeframe: {timeframe}")
                    logger.error(f"Available timeframes: {list(TIMEFRAME_MAP.keys())}")
                    logger.error(f"Symbols: {symbols}")
                    logger.error(f"Timeframes: {timeframes}")
                    continue

                # Convert symbol format (e.g., "EUR/USD" -> "EURUSD", "BTC/USD" -> "BTCUSD")
                mt5_symbol = symbol.replace("/", "").replace("-", "")

                # Get candle data from MT5 API
                logger.info(f"Fetching {mt5_symbol} {timeframe} from MT5 API")
                candle_data = await get_mt5_candle_data(mt5_symbol, timeframe)

                if candle_data:
                    await save_candle_data(symbol, timeframe, candle_data)
                    logger.info(f"Collected {len(candle_data)} candles for {symbol} {timeframe} from MT5")
                else:
                    logger.warning(f"Failed to get data from MT5 for {symbol} {timeframe}")

            except Exception as e:
                logger.error(f"Error collecting data for {symbol} {timeframe}: {e}", exc_info=True)

    logger.info("Market data collection completed")

async def get_mt5_candle_data(symbol, timeframe, count=100):
    """
    Get candle data from MT5 API

    Args:
        symbol (str): Symbol to get data for
        timeframe (str): Timeframe to get data for
        count (int): Number of candles to fetch

    Returns:
        list: List of candle data dictionaries
    """
    try:
        # Convert timeframe to MT5 format
        mt5_timeframe = TIMEFRAME_MAP.get(timeframe)
        if not mt5_timeframe:
            logger.error(f"Unsupported timeframe: {timeframe}")
            return []

        logger.info(f"Requesting {symbol} {mt5_timeframe} candles from MT5 API")

        # Skip health check since MT5 API doesn't have /health endpoint
        # The API is working as confirmed by direct testing
        logger.info("Proceeding with MT5 API data request...")

        async with httpx.AsyncClient(timeout=60.0) as client:
            # Request candle data
            response = await client.post(
                MT5_CANDLES_URL,
                json={
                    "symbol": symbol,
                    "timeframe": mt5_timeframe,
                    "count": count
                }
            )

            if response.status_code != 200:
                logger.error(f"Failed to request candles: {response.status_code}")
                logger.debug(f"Response content: {response.text[:200]}")
                return []

            # Parse JSON response with error handling
            try:
                if not response.text.strip():
                    logger.error("Empty response from MT5 API")
                    return []
                data = response.json()
            except ValueError as e:
                logger.error(f"Invalid JSON response from MT5 API: {e}")
                logger.debug(f"Response content: {response.text[:500]}")
                return []
            job_id = data.get("job_id")

            if not job_id:
                logger.error("No job_id received from candles request")
                return []

            # Wait for job completion (reduced initial wait time)
            logger.info(f"Waiting for job {job_id} to complete...")
            await asyncio.sleep(5)

            # Try to get job result with retries (increased retries)
            max_retries = 6
            for attempt in range(max_retries):
                try:
                    job_response = await client.post(
                        MT5_JOB_STATUS_URL,
                        json={"job_id": job_id}
                    )

                    if job_response.status_code != 200:
                        logger.error(f"Failed to get job status: {job_response.status_code}")
                        logger.error(f"Response text: {job_response.text}")

                        # Enhanced handling for server errors (500) including MT5Connector issues
                        if job_response.status_code == 500:
                            response_text = job_response.text

                            # Check for specific MT5Connector logger attribute error
                            if "MT5Connector" in response_text and "logger" in response_text:
                                logger.warning("🔧 Detected MT5Connector logger attribute error - external service issue")
                                logger.warning("💡 This is a known issue with the external MT5 API service")
                                logger.warning("📊 Market data collection will be skipped for this symbol/timeframe")

                                # For this specific error, don't retry as it's an external service issue
                                logger.info(f"⏭️ Skipping {symbol} {timeframe} due to external MT5 service error")
                                return []
                            elif "AttributeError" in response_text:
                                logger.warning("🔧 AttributeError detected in MT5 service - likely service misconfiguration")
                                logger.warning("💡 This indicates an issue with the external MT5 API service setup")
                            else:
                                logger.warning("🚨 Server error (500) - likely MT5Connector internal issue")
                                logger.debug(f"Response details: {response_text[:200]}")

                            # For other server errors, wait longer before retry
                            if attempt < max_retries - 1:
                                backoff_delay = 15 + (attempt * 5)  # Progressive backoff
                                logger.info(f"⏳ Waiting {backoff_delay} seconds before retry due to server error...")
                                await asyncio.sleep(backoff_delay)
                                continue
                        else:
                            if attempt < max_retries - 1:
                                await asyncio.sleep(10)
                                continue
                        return []

                    try:
                        job_data = job_response.json()
                    except json.JSONDecodeError as e:
                        logger.error(f"Failed to parse job status response as JSON: {e}")
                        logger.error(f"Response text: {job_response.text}")
                        if attempt < max_retries - 1:
                            await asyncio.sleep(10)
                            continue
                        return []

                    status = job_data.get("status")

                    if status == "finished":
                        # Try different possible response structures
                        candles = []

                        # Check for candles in different locations
                        if "result" in job_data:
                            result = job_data["result"]
                            if isinstance(result, list):
                                candles = result
                            elif isinstance(result, dict):
                                candles = result.get("candles", [])
                        elif "candles" in job_data:
                            candles = job_data["candles"]
                        elif "data" in job_data:
                            candles = job_data["data"]

                        if candles:
                            logger.info(f"✅ Received {len(candles)} candles for {symbol} {timeframe}")
                            processed_candles = process_mt5_candles(candles)
                            logger.info(f"✅ Processed {len(processed_candles)} candles for {symbol} {timeframe}")
                            return processed_candles
                        else:
                            logger.warning(f"❌ No candles received for {symbol} {timeframe}")
                            logger.warning(f"Job response structure: {list(job_data.keys())}")
                            logger.warning(f"Full job response: {job_data}")
                            return []

                    elif status == "pending":
                        logger.info(f"Job still pending, waiting... (attempt {attempt + 1})")
                        await asyncio.sleep(10)  # Reduced wait time between retries
                        continue

                    else:
                        logger.error(f"Job failed with status: {status}")
                        return []

                except Exception as e:
                    logger.error(f"Error checking job status (attempt {attempt + 1}): {type(e).__name__}: {str(e)}")
                    logger.error(f"Job ID: {job_id}, Symbol: {symbol}, Timeframe: {timeframe}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(10)
                        continue
                    return []

            logger.error(f"❌ Job {job_id} did not complete after {max_retries} attempts")
            logger.error(f"📊 No candle data available for {symbol} {timeframe} - MT5 API job failed")
            logger.warning("💡 This might be due to:")
            logger.warning("   - MT5Connector logger attribute error on the external API service")
            logger.warning("   - External MT5 service being temporarily unavailable")
            logger.warning("   - Network connectivity issues with the MT5 API")
            logger.warning("   - Symbol or timeframe not supported by the MT5 service")
            return []

    except Exception as e:
        logger.error(f"Error fetching MT5 candle data for {symbol} {timeframe}: {e}", exc_info=True)
        logger.error(f"No candle data available for {symbol} {timeframe} - MT5 API request failed")
        return []

def process_mt5_candles(candles):
    """
    Process MT5 candle data into our format

    Args:
        candles (list): Raw candle data from MT5 API

    Returns:
        list: Processed candle data
    """
    processed_candles = []

    for candle in candles:
        try:
            # Handle both dictionary format (current API) and array format (legacy)
            if isinstance(candle, dict):
                # New dictionary format from MT5 API
                processed_candle = {
                    "timestamp": datetime.fromtimestamp(candle.get("time", 0), tz=timezone.utc),
                    "open": float(candle.get("open", 0)),
                    "high": float(candle.get("high", 0)),
                    "low": float(candle.get("low", 0)),
                    "close": float(candle.get("close", 0)),
                    "volume": float(candle.get("tick_volume", candle.get("real_volume", 0)))
                }
                processed_candles.append(processed_candle)

            elif isinstance(candle, (list, tuple)) and len(candle) >= 5:
                # Legacy array format: [timestamp, open, high, low, close, volume]
                processed_candle = {
                    "timestamp": datetime.fromtimestamp(candle[0], tz=timezone.utc),
                    "open": float(candle[1]),
                    "high": float(candle[2]),
                    "low": float(candle[3]),
                    "close": float(candle[4]),
                    "volume": float(candle[5]) if len(candle) > 5 else 0.0
                }
                processed_candles.append(processed_candle)
            else:
                logger.warning(f"Unknown candle format: {candle}")

        except (ValueError, IndexError, KeyError) as e:
            logger.warning(f"Error processing candle {candle}: {e}")
            continue

    return processed_candles



async def save_candle_data(symbol, timeframe, data):
    """
    Save candle data to database

    Args:
        symbol (str): Symbol the data is for
        timeframe (str): Timeframe the data is for
        data (list or pandas.DataFrame): Candle data
    """
    from src.database.setup import is_sqlite_db

    # Handle list format (from MT5 API)
    if isinstance(data, list):
        await save_candle_data_from_list(symbol, timeframe, data)
        return

    # Handle DataFrame format (legacy)
    await save_candle_data_from_dataframe(symbol, timeframe, data)

async def save_candle_data_from_list(symbol, timeframe, candle_list):
    """
    Save candle data from list format to database

    Args:
        symbol (str): Symbol the data is for
        timeframe (str): Timeframe the data is for
        candle_list (list): List of candle dictionaries
    """
    from src.database.setup import is_sqlite_db

    if not candle_list:
        logger.warning(f"No candle data to save for {symbol} {timeframe}")
        return

    async for db in get_async_db():
        try:
            candles_added = 0

            for candle_dict in candle_list:
                try:
                    # Check if candle already exists
                    timestamp = candle_dict["timestamp"]

                    if is_sqlite_db():
                        existing_result = db.execute(
                            select(CandleData).where(
                                CandleData.symbol == symbol,
                                CandleData.timeframe == timeframe,
                                CandleData.timestamp == timestamp
                            )
                        )
                    else:
                        existing_result = await db.execute(
                            select(CandleData).where(
                                CandleData.symbol == symbol,
                                CandleData.timeframe == timeframe,
                                CandleData.timestamp == timestamp
                            )
                        )

                    existing_candle = existing_result.scalars().first()
                    if existing_candle:
                        continue  # Skip duplicate

                    # Create new candle data record
                    candle = CandleData(
                        symbol=symbol,
                        timeframe=timeframe,
                        timestamp=timestamp,
                        open=float(candle_dict["open"]),
                        high=float(candle_dict["high"]),
                        low=float(candle_dict["low"]),
                        close=float(candle_dict["close"]),
                        volume=float(candle_dict.get("volume", 0)),
                        created_at=datetime.now(timezone.utc)
                    )

                    db.add(candle)
                    candles_added += 1

                except (ValueError, TypeError, KeyError) as e:
                    logger.warning(f"Skipping invalid candle data for {symbol} {timeframe}: {e}")
                    continue

            # Commit changes
            if is_sqlite_db():
                db.commit()
            else:
                await db.commit()

            logger.info(f"Added {candles_added} new candles for {symbol} {timeframe}")

        except Exception as e:
            if is_sqlite_db():
                db.rollback()
            else:
                await db.rollback()
            logger.error(f"Error saving candle data for {symbol} {timeframe}: {e}", exc_info=True)
            raise

async def save_candle_data_from_dataframe(symbol, timeframe, data):
    """
    Save candle data from DataFrame format to database (legacy support)

    Args:
        symbol (str): Symbol the data is for
        timeframe (str): Timeframe the data is for
        data (pandas.DataFrame): DataFrame containing market data
    """
    from src.database.setup import is_sqlite_db

    # Check if the DataFrame has the required columns
    required_columns = ["Open", "High", "Low", "Close"]

    # Check column names and standardize them if needed
    column_mapping = {}

    # Check for date/timestamp column
    date_column = None
    for col in data.columns:
        if col in ["Date", "Datetime", "date", "datetime", "timestamp", "time"]:
            date_column = col
            break

    if date_column is None and data.index.name in ["Date", "Datetime", "date", "datetime", "timestamp", "time"]:
        # If date is in the index, reset index to make it a column
        date_column = data.index.name
        data = data.reset_index()

    # If we still don't have a date column, try to use the index
    if date_column is None and isinstance(data.index, pd.DatetimeIndex):
        data = data.reset_index()
        date_column = "index"

    if date_column is None:
        logger.error(f"No date/timestamp column found in data for {symbol} {timeframe}")
        return

    # Check for OHLC columns with different naming conventions
    for col in data.columns:
        col_lower = str(col).lower()
        if "open" in col_lower:
            column_mapping[col] = "Open"
        elif "high" in col_lower:
            column_mapping[col] = "High"
        elif "low" in col_lower:
            column_mapping[col] = "Low"
        elif "close" in col_lower:
            column_mapping[col] = "Close"
        elif "volume" in col_lower:
            column_mapping[col] = "Volume"

    # Rename columns if needed
    if column_mapping:
        data = data.rename(columns=column_mapping)

    # Check if we have all required columns
    missing_columns = [col for col in required_columns if col not in data.columns]
    if missing_columns:
        logger.error(f"Missing required columns {missing_columns} in data for {symbol} {timeframe}")
        return

    async for db in get_async_db():
        try:
            # Get existing timestamps for this symbol and timeframe
            # Handle both synchronous and asynchronous operations
            if is_sqlite_db():
                # Synchronous operation for SQLite
                result = db.execute(
                    select(CandleData.timestamp)
                    .where(
                        CandleData.symbol == symbol,
                        CandleData.timeframe == timeframe
                    )
                )
                existing_timestamps = {row[0] for row in result.all()}
            else:
                # Asynchronous operation for other databases
                result = await db.execute(
                    select(CandleData.timestamp)
                    .where(
                        CandleData.symbol == symbol,
                        CandleData.timeframe == timeframe
                    )
                )
                existing_timestamps = {row[0] for row in result.all()}

            # Process each row in the dataframe
            candles_added = 0
            for _, row in data.iterrows():
                timestamp = row[date_column]

                # Skip if we already have this candle
                if timestamp in existing_timestamps:
                    continue

                # Skip rows with NaN values
                if (pd.isna(row["Open"]) or pd.isna(row["High"]) or
                    pd.isna(row["Low"]) or pd.isna(row["Close"])):
                    continue

                try:
                    # Create new candle data record
                    candle = CandleData(
                        symbol=symbol,
                        timeframe=timeframe,
                        timestamp=timestamp,
                        open=float(row["Open"]),
                        high=float(row["High"]),
                        low=float(row["Low"]),
                        close=float(row["Close"]),
                        volume=float(row["Volume"]) if "Volume" in row.keys() and not pd.isna(row["Volume"]) else None,
                        created_at=datetime.now(timezone.utc)
                    )

                    db.add(candle)
                    candles_added += 1
                except (ValueError, TypeError) as e:
                    logger.warning(f"Skipping invalid data row for {symbol} {timeframe}: {e}")

            # Commit changes
            if is_sqlite_db():
                db.commit()
            else:
                await db.commit()

            logger.info(f"Added {candles_added} new candles for {symbol} {timeframe}")

        except Exception as e:
            # Handle both synchronous and asynchronous rollback
            if is_sqlite_db():
                db.rollback()
            else:
                await db.rollback()
            logger.error(f"Error saving candle data for {symbol} {timeframe}: {e}", exc_info=True)
            raise
