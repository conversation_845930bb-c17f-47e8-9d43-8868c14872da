# Afghanistan Farsi Prompts Injection

This directory contains scripts to inject Afghanistan Farsi (fa-af) language prompts into the MignalyBot database running in Docker.

## Files

- `inject_afghanistan_prompts.py` - Python script that connects to the database and inserts the prompts
- `docker_inject_prompts.sh` - Linux/Mac shell script to execute the injection in Docker container
- `docker_inject_prompts.bat` - Windows batch script to execute the injection in Docker container

## Usage

### For Linux/Mac:
```bash
./docker_inject_prompts.sh
```

### For Windows:
```cmd
docker_inject_prompts.bat
```

### Manual execution:
If you prefer to run the commands manually:

1. Copy the script to the container:
```bash
docker cp inject_afghanistan_prompts.py mignalybot:/app/inject_afghanistan_prompts.py
```

2. Execute the script inside the container:
```bash
docker exec mignalybot python /app/inject_afghanistan_prompts.py
```

3. Clean up:
```bash
docker exec mignalybot rm -f /app/inject_afghanistan_prompts.py
```

## What the script does

The injection script adds 8 prompt templates for Afghanistan Farsi (fa-af) language:

1. **signals** - Trading signal analysis prompts
2. **signal_update** - Signal update prompts  
3. **market_analysis** - Market analysis prompts
4. **news** - News analysis prompts
5. **events** - Economic events analysis prompts
6. **performance** - Performance report prompts
7. **greeting** - Daily greeting prompts
8. **analysis** - Technical analysis prompts

## Requirements

- Docker must be running
- MignalyBot container must be running (container name: `mignalybot`)
- The scripts must be run from the host machine (not inside the container)

## Troubleshooting

- **Container not found**: Make sure the MignalyBot container is running with `docker ps`
- **Permission denied**: Make sure Docker is running and you have permission to execute Docker commands
- **Script already exists**: The script will skip prompts that already exist in the database

## Verification

After running the script, you can verify the prompts were added by checking the application logs. The error messages like:

```
WARNING - ⚠️ No prompt template found for news (fa-af) in database
ERROR - ❌ No prompt template found for news analysis (fa-af)
```

Should no longer appear for fa-af language requests.
