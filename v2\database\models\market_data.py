"""
Market data models for MignalyBot v2

Optimized models for:
- Candle data
- Trading signals
- Market indicators
"""

from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy import (
    Column, Integer, String, Float, Boolean, DateTime, Text,
    ForeignKey, Index, UniqueConstraint
)
from sqlalchemy.orm import relationship
import pytz

from .base import Base
from .core import SignalStatus


class CandleData(Base):
    """OHLCV candle data model"""
    __tablename__ = "candle_data"
    
    # Market identifiers
    symbol = Column(String(20), nullable=False, index=True)
    timeframe = Column(String(10), nullable=False, index=True)
    timestamp = Column(DateTime(timezone=True), nullable=False, index=True)
    
    # OHLCV data
    open = Column(Float, nullable=False)
    high = Column(Float, nullable=False)
    low = Column(Float, nullable=False)
    close = Column(Float, nullable=False, index=True)  # Index for price queries
    volume = Column(Float, nullable=False)
    
    # Additional market data
    vwap = Column(Float, nullable=True)  # Volume Weighted Average Price
    trades_count = Column(Integer, nullable=True)
    
    # Data source metadata
    source = Column(String(50), nullable=True, index=True)
    data_quality = Column(Float, default=1.0)  # Quality score 0-1
    
    # Unique constraint to prevent duplicates
    __table_args__ = (
        UniqueConstraint('symbol', 'timeframe', 'timestamp', name='uq_candle_data'),
        Index('idx_candle_symbol_timeframe_timestamp', symbol, timeframe, timestamp.desc()),
        Index('idx_candle_timestamp_symbol', timestamp.desc(), symbol),
        Index('idx_candle_close_timestamp', close, timestamp.desc()),
    )
    
    @property
    def price_change(self) -> float:
        """Calculate price change percentage"""
        if self.open == 0:
            return 0.0
        return ((self.close - self.open) / self.open) * 100
    
    @property
    def is_bullish(self) -> bool:
        """Check if candle is bullish"""
        return self.close > self.open
    
    @property
    def body_size(self) -> float:
        """Calculate candle body size"""
        return abs(self.close - self.open)
    
    @property
    def upper_shadow(self) -> float:
        """Calculate upper shadow size"""
        return self.high - max(self.open, self.close)
    
    @property
    def lower_shadow(self) -> float:
        """Calculate lower shadow size"""
        return min(self.open, self.close) - self.low


class TradingSignal(Base):
    """Trading signal model"""
    __tablename__ = "trading_signals"
    
    # Foreign key to strategy
    strategy_id = Column(Integer, ForeignKey("strategies.id"), nullable=False, index=True)
    
    # Signal details
    symbol = Column(String(20), nullable=False, index=True)
    timeframe = Column(String(10), nullable=False, index=True)
    direction = Column(String(10), nullable=False, index=True)  # buy/sell
    signal_type = Column(String(50), nullable=False, index=True)  # entry/exit/tp/sl
    
    # Price levels
    entry_price = Column(Float, nullable=False)
    stop_loss = Column(Float, nullable=True)
    take_profit = Column(Float, nullable=True)
    current_price = Column(Float, nullable=True)
    
    # Signal timing
    signal_time = Column(DateTime(timezone=True), nullable=False, index=True)
    entry_time = Column(DateTime(timezone=True), nullable=True, index=True)
    exit_time = Column(DateTime(timezone=True), nullable=True, index=True)
    
    # Signal status and performance
    status = Column(String(20), default=SignalStatus.ACTIVE.value, index=True)
    confidence = Column(Float, default=0.5)  # 0-1 confidence score
    risk_reward_ratio = Column(Float, nullable=True)
    
    # Performance tracking
    profit_loss = Column(Float, nullable=True)  # Percentage
    profit_loss_pips = Column(Float, nullable=True)
    max_favorable_excursion = Column(Float, nullable=True)
    max_adverse_excursion = Column(Float, nullable=True)
    
    # Signal metadata
    signal_strength = Column(Float, default=0.5)  # 0-1 signal strength
    market_conditions = Column(String(100), nullable=True)  # trending/ranging/volatile
    ai_analysis = Column(Text, nullable=True)
    
    # Relationships
    strategy = relationship("Strategy", back_populates="signals")
    posts = relationship("Post", back_populates="trading_signal")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_signal_strategy_status', strategy_id, status),
        Index('idx_signal_symbol_timeframe', symbol, timeframe),
        Index('idx_signal_time_status', signal_time.desc(), status),
        Index('idx_signal_performance', profit_loss.desc()),
        Index('idx_signal_confidence', confidence.desc()),
    )
    
    @property
    def is_profitable(self) -> bool:
        """Check if signal is profitable"""
        return self.profit_loss is not None and self.profit_loss > 0
    
    @property
    def duration_hours(self) -> Optional[float]:
        """Calculate signal duration in hours"""
        if self.entry_time and self.exit_time:
            delta = self.exit_time - self.entry_time
            return delta.total_seconds() / 3600
        return None
    
    @property
    def risk_amount(self) -> Optional[float]:
        """Calculate risk amount (entry to stop loss)"""
        if self.entry_price and self.stop_loss:
            return abs(self.entry_price - self.stop_loss) / self.entry_price * 100
        return None
    
    @property
    def reward_amount(self) -> Optional[float]:
        """Calculate reward amount (entry to take profit)"""
        if self.entry_price and self.take_profit:
            return abs(self.take_profit - self.entry_price) / self.entry_price * 100
        return None


class MarketIndicator(Base):
    """Market indicator values model"""
    __tablename__ = "market_indicators"
    
    # Market identifiers
    symbol = Column(String(20), nullable=False, index=True)
    timeframe = Column(String(10), nullable=False, index=True)
    timestamp = Column(DateTime(timezone=True), nullable=False, index=True)
    
    # Technical indicators
    sma_20 = Column(Float, nullable=True)
    sma_50 = Column(Float, nullable=True)
    sma_200 = Column(Float, nullable=True)
    ema_12 = Column(Float, nullable=True)
    ema_26 = Column(Float, nullable=True)
    
    # Momentum indicators
    rsi_14 = Column(Float, nullable=True)
    macd = Column(Float, nullable=True)
    macd_signal = Column(Float, nullable=True)
    macd_histogram = Column(Float, nullable=True)
    
    # Volatility indicators
    bollinger_upper = Column(Float, nullable=True)
    bollinger_middle = Column(Float, nullable=True)
    bollinger_lower = Column(Float, nullable=True)
    atr_14 = Column(Float, nullable=True)
    
    # Volume indicators
    volume_sma_20 = Column(Float, nullable=True)
    volume_ratio = Column(Float, nullable=True)
    
    # Market sentiment
    fear_greed_index = Column(Float, nullable=True)
    volatility_index = Column(Float, nullable=True)
    
    # Unique constraint to prevent duplicates
    __table_args__ = (
        UniqueConstraint('symbol', 'timeframe', 'timestamp', name='uq_market_indicators'),
        Index('idx_indicator_symbol_timeframe_timestamp', symbol, timeframe, timestamp.desc()),
        Index('idx_indicator_rsi', rsi_14),
        Index('idx_indicator_macd', macd),
    )
