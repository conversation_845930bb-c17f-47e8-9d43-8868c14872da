{% extends "base.html" %}

{% block title %}Trading Signals - MignalyBot Admin{% endblock %}

{% block page_title %}Trading Signals Management{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Trading Signals</h5>
        <div>
            <button type="button" class="btn btn-primary" id="refreshSignalsBtn">
                <i class="fas fa-sync-alt"></i> Refresh
            </button>
            <button type="button" class="btn btn-success" id="processStrategiesBtn">
                <i class="fas fa-cogs"></i> Process Strategies
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="mb-3">
            <div class="row">
                <div class="col-md-3">
                    <select class="form-select" id="statusFilter">
                        <option value="">All Statuses</option>
                        <option value="active">Active</option>
                        <option value="tp_hit">TP Hit</option>
                        <option value="sl_hit">SL Hit</option>
                        <option value="expired">Expired</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="symbolFilter">
                        <option value="">All Symbols</option>
                        <!-- Symbols will be populated dynamically -->
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="strategyFilter">
                        <option value="">All Strategies</option>
                        <!-- Strategies will be populated dynamically -->
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="directionFilter">
                        <option value="">All Directions</option>
                        <option value="buy">Buy</option>
                        <option value="sell">Sell</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-hover" id="signalsTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Symbol</th>
                        <th>Direction</th>
                        <th>Entry</th>
                        <th>SL</th>
                        <th>TP</th>
                        <th>R:R</th>
                        <th>Status</th>
                        <th>P/L</th>
                        <th>Strategy</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="11" class="text-center">Loading signals...</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="d-flex justify-content-between align-items-center mt-3">
            <div>
                <span id="signalsCount">0</span> trading signals
            </div>
            <div>
                <nav aria-label="Signals pagination">
                    <ul class="pagination" id="signalsPagination">
                        <!-- Pagination will be populated dynamically -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Signal Detail Modal -->
<div class="modal fade" id="signalDetailModal" tabindex="-1" aria-labelledby="signalDetailModalTitle" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="signalDetailModalTitle">Signal Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <h5 id="signalSymbol"></h5>
                    <div class="d-flex justify-content-between text-muted small">
                        <div>Strategy: <span id="signalStrategy"></span></div>
                        <div>Timeframe: <span id="signalTimeframe"></span></div>
                        <div>Created: <span id="signalCreatedAt"></span></div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-light">Signal Details</div>
                            <div class="card-body">
                                <table class="table table-sm">
                                    <tr>
                                        <th>Direction:</th>
                                        <td id="signalDirection"></td>
                                    </tr>
                                    <tr>
                                        <th>Entry Price:</th>
                                        <td id="signalEntryPrice"></td>
                                    </tr>
                                    <tr>
                                        <th>Stop Loss:</th>
                                        <td id="signalStopLoss"></td>
                                    </tr>
                                    <tr>
                                        <th>Take Profit:</th>
                                        <td id="signalTakeProfit"></td>
                                    </tr>
                                    <tr>
                                        <th>Risk/Reward:</th>
                                        <td id="signalRiskReward"></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-light">Status</div>
                            <div class="card-body">
                                <table class="table table-sm">
                                    <tr>
                                        <th>Status:</th>
                                        <td id="signalStatus"></td>
                                    </tr>
                                    <tr>
                                        <th>Entry Time:</th>
                                        <td id="signalEntryTime"></td>
                                    </tr>
                                    <tr>
                                        <th>Exit Time:</th>
                                        <td id="signalExitTime"></td>
                                    </tr>
                                    <tr>
                                        <th>Exit Price:</th>
                                        <td id="signalExitPrice"></td>
                                    </tr>
                                    <tr>
                                        <th>Profit/Loss:</th>
                                        <td id="signalProfitLoss"></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <h6>Notes</h6>
                    <div id="signalNotes" class="p-3 bg-light rounded"></div>
                </div>

                <div class="mb-3" id="signalChartContainer">
                    <h6>Chart</h6>
                    <div id="signalChart" class="text-center"></div>
                </div>

                <div class="mb-3">
                    <button id="generateSignalPostBtn" class="btn btn-sm btn-success">
                        <i class="fas fa-magic"></i> Generate Post
                    </button>
                    <button id="generateUpdatePostBtn" class="btn btn-sm btn-info">
                        <i class="fas fa-chart-line"></i> Generate Performance Update
                    </button>
                    <button id="updateSignalBtn" class="btn btn-sm btn-primary">
                        <i class="fas fa-edit"></i> Update Signal
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Update Signal Modal -->
<div class="modal fade" id="updateSignalModal" tabindex="-1" aria-labelledby="updateSignalModalTitle" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="updateSignalModalTitle">Update Signal</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="updateSignalForm">
                    <input type="hidden" id="updateSignalId">

                    <div class="mb-3">
                        <label for="updateSignalStatus" class="form-label">Status</label>
                        <select class="form-select" id="updateSignalStatus" name="status" required>
                            <option value="active">Active</option>
                            <option value="tp_hit">TP Hit</option>
                            <option value="sl_hit">SL Hit</option>
                            <option value="expired">Expired</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="updateSignalExitPrice" class="form-label">Exit Price</label>
                        <input type="number" class="form-control" id="updateSignalExitPrice" name="exit_price" step="0.00000001">
                    </div>

                    <div class="mb-3">
                        <label for="updateSignalExitTime" class="form-label">Exit Time</label>
                        <input type="datetime-local" class="form-control" id="updateSignalExitTime" name="exit_time">
                    </div>

                    <div class="mb-3">
                        <label for="updateSignalNotes" class="form-label">Notes</label>
                        <textarea class="form-control" id="updateSignalNotes" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveUpdateSignalBtn">Save Changes</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let currentPage = 1;
    const pageSize = 20;
    let totalSignals = 0;

    $(document).ready(function() {
        // Load signals
        loadSignals();

        // Set up event handlers
        $('#refreshSignalsBtn').click(function() {
            loadSignals();
        });

        $('#processStrategiesBtn').click(function() {
            processStrategies();
        });

        $('#statusFilter, #symbolFilter, #strategyFilter, #directionFilter').change(function() {
            currentPage = 1;
            loadSignals();
        });

        // Set up event handlers for the modal buttons
        $('#updateSignalBtn').click(function() {
            const signalId = $(this).data('signal-id');
            showUpdateSignalModal(signalId);
        });

        $('#saveUpdateSignalBtn').click(function() {
            saveSignalUpdate();
        });

        $('#generateSignalPostBtn').click(function() {
            const signalId = $(this).data('signal-id');
            generateSignalPost(signalId);
        });

        $('#generateUpdatePostBtn').click(function() {
            const signalId = $(this).data('signal-id');
            generateSignalUpdate(signalId);
        });

        // Initialize tooltips
        $('[data-bs-toggle="tooltip"]').tooltip();
    });

    function loadSignals() {
        const status = $('#statusFilter').val();
        const symbol = $('#symbolFilter').val();
        const strategy = $('#strategyFilter').val();
        const direction = $('#directionFilter').val();

        $.ajax({
            url: '/api/signals',
            type: 'GET',
            data: {
                status: status,
                symbol: symbol,
                strategy_id: strategy,
                direction: direction,
                page: currentPage,
                page_size: pageSize
            },
            success: function(data) {
                totalSignals = data.total;
                $('#signalsCount').text(data.total);

                let tableHtml = '';

                if (data.signals && data.signals.length > 0) {
                    data.signals.forEach(function(signal) {
                        const statusBadge = getStatusBadge(signal.status);
                        const directionBadge = getDirectionBadge(signal.direction);

                        // Safely handle profit_loss which might be undefined
                        let profitLoss = 'N/A';
                        if (signal.profit_loss !== null && signal.profit_loss !== undefined) {
                            try {
                                profitLoss = `${parseFloat(signal.profit_loss).toFixed(2)}%`;
                            } catch (e) {
                                console.error('Error formatting profit_loss:', e);
                            }
                        }

                        tableHtml += `
                            <tr>
                                <td>${signal.id}</td>
                                <td>${signal.symbol}</td>
                                <td>${directionBadge}</td>
                                <td>${signal.entry_price}</td>
                                <td>${signal.stop_loss}</td>
                                <td>${signal.take_profit}</td>
                                <td>${signal.risk_reward ? signal.risk_reward.toFixed(2) : 'N/A'}</td>
                                <td>${statusBadge}</td>
                                <td>${profitLoss}</td>
                                <td>${signal.strategy_name || 'N/A'}</td>
                                <td>
                                    <button class="btn btn-sm btn-primary signal-detail-btn" data-signal-id="${signal.id}">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-success generate-signal-post-btn" data-signal-id="${signal.id}">
                                        <i class="fas fa-magic"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger delete-signal-btn" data-signal-id="${signal.id}" title="Delete Signal">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        `;
                    });
                } else {
                    tableHtml = '<tr><td colspan="11" class="text-center">No signals found</td></tr>';
                }

                $('#signalsTable tbody').html(tableHtml);

                // Set up pagination
                updatePagination(data.total);

                // Set up event handlers for the newly created elements
                $('.signal-detail-btn').click(function() {
                    const signalId = $(this).data('signal-id');
                    showSignalDetail(signalId);
                });

                $('.generate-signal-post-btn').click(function() {
                    const signalId = $(this).data('signal-id');
                    generateSignalPost(signalId);
                });

                $('.delete-signal-btn').click(function() {
                    const signalId = $(this).data('signal-id');
                    deleteSignal(signalId);
                });

                // Populate filters if they're empty
                if ($('#symbolFilter option').length <= 1) {
                    const symbols = [...new Set(data.signals.map(item => item.symbol))];
                    let symbolOptions = '<option value="">All Symbols</option>';
                    symbols.forEach(symbol => {
                        symbolOptions += `<option value="${symbol}">${symbol}</option>`;
                    });
                    $('#symbolFilter').html(symbolOptions);
                }

                if ($('#strategyFilter option').length <= 1) {
                    const strategies = [...new Set(data.signals
                        .filter(item => item.strategy_id && item.strategy_name)
                        .map(item => ({ id: item.strategy_id, name: item.strategy_name })))];
                    let strategyOptions = '<option value="">All Strategies</option>';
                    strategies.forEach(strategy => {
                        strategyOptions += `<option value="${strategy.id}">${strategy.name}</option>`;
                    });
                    $('#strategyFilter').html(strategyOptions);
                }
            },
            error: function(xhr) {
                $('#signalsTable tbody').html('<tr><td colspan="11" class="text-center text-danger">Error loading signals</td></tr>');
                console.error('Error loading signals:', xhr);
            }
        });
    }

    function getStatusBadge(status) {
        switch (status) {
            case 'active':
                return '<span class="badge bg-primary">Active</span>';
            case 'tp_hit':
                return '<span class="badge bg-success">TP Hit</span>';
            case 'sl_hit':
                return '<span class="badge bg-danger">SL Hit</span>';
            case 'expired':
                return '<span class="badge bg-secondary">Expired</span>';
            case 'cancelled':
                return '<span class="badge bg-warning text-dark">Cancelled</span>';
            default:
                return '<span class="badge bg-secondary">Unknown</span>';
        }
    }

    function getDirectionBadge(direction) {
        if (direction === 'buy') {
            return '<span class="badge bg-success">BUY</span>';
        } else if (direction === 'sell') {
            return '<span class="badge bg-danger">SELL</span>';
        } else {
            return '<span class="badge bg-secondary">Unknown</span>';
        }
    }

    function updatePagination(total) {
        const totalPages = Math.ceil(total / pageSize);
        let paginationHtml = '';

        if (totalPages > 1) {
            // Previous button
            paginationHtml += `
                <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="${currentPage - 1}">Previous</a>
                </li>
            `;

            // Page numbers
            for (let i = 1; i <= totalPages; i++) {
                if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                    paginationHtml += `
                        <li class="page-item ${i === currentPage ? 'active' : ''}">
                            <a class="page-link" href="#" data-page="${i}">${i}</a>
                        </li>
                    `;
                } else if (i === currentPage - 3 || i === currentPage + 3) {
                    paginationHtml += `
                        <li class="page-item disabled">
                            <a class="page-link" href="#">...</a>
                        </li>
                    `;
                }
            }

            // Next button
            paginationHtml += `
                <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="${currentPage + 1}">Next</a>
                </li>
            `;
        }

        $('#signalsPagination').html(paginationHtml);

        // Set up event handlers for pagination
        $('.page-link').click(function(e) {
            e.preventDefault();
            const page = $(this).data('page');
            if (page && page !== currentPage && page > 0 && page <= totalPages) {
                currentPage = page;
                loadSignals();
            }
        });
    }

    function showSignalDetail(signalId) {
        $.ajax({
            url: `/api/signals/${signalId}`,
            type: 'GET',
            success: function(signal) {
                $('#signalSymbol').text(`${signal.symbol} (${signal.timeframe})`);
                $('#signalStrategy').text(signal.strategy_name || 'N/A');
                $('#signalTimeframe').text(signal.timeframe);
                $('#signalCreatedAt').text(new Date(signal.created_at).toLocaleString());

                $('#signalDirection').html(getDirectionBadge(signal.direction));
                $('#signalEntryPrice').text(signal.entry_price);
                $('#signalStopLoss').text(signal.stop_loss);
                $('#signalTakeProfit').text(signal.take_profit);
                $('#signalRiskReward').text(signal.risk_reward ? signal.risk_reward.toFixed(2) : 'N/A');

                $('#signalStatus').html(getStatusBadge(signal.status));
                $('#signalEntryTime').text(new Date(signal.entry_time).toLocaleString());
                $('#signalExitTime').text(signal.exit_time ? new Date(signal.exit_time).toLocaleString() : 'N/A');
                $('#signalExitPrice').text(signal.exit_price || 'N/A');

                // Safely handle profit_loss
                let profitLossText = 'N/A';
                if (signal.profit_loss !== null && signal.profit_loss !== undefined) {
                    try {
                        profitLossText = `${parseFloat(signal.profit_loss).toFixed(2)}%`;
                    } catch (e) {
                        console.error('Error formatting profit_loss in detail view:', e);
                    }
                }
                $('#signalProfitLoss').text(profitLossText);

                $('#signalNotes').html(signal.notes || '<em>No notes available</em>');

                // Set chart image if available
                if (signal.chart_image) {
                    $('#signalChart').html(`<img src="${signal.chart_image}" class="img-fluid rounded" alt="Chart">`);
                    $('#signalChartContainer').show();
                } else {
                    $('#signalChartContainer').hide();
                }

                // Set up buttons
                $('#generateSignalPostBtn').data('signal-id', signal.id);
                $('#generateUpdatePostBtn').data('signal-id', signal.id);
                $('#updateSignalBtn').data('signal-id', signal.id);

                // Show modal
                $('#signalDetailModal').modal('show');
            },
            error: function(xhr) {
                alert('Error loading signal details: ' + xhr.responseJSON.detail);
            }
        });
    }

    function showUpdateSignalModal(signalId) {
        $.ajax({
            url: `/api/signals/${signalId}`,
            type: 'GET',
            success: function(signal) {
                $('#updateSignalId').val(signal.id);
                $('#updateSignalStatus').val(signal.status);
                $('#updateSignalExitPrice').val(signal.exit_price || '');

                // Format date for datetime-local input
                if (signal.exit_time) {
                    const exitTime = new Date(signal.exit_time);
                    const formattedDate = exitTime.toISOString().slice(0, 16);
                    $('#updateSignalExitTime').val(formattedDate);
                } else {
                    $('#updateSignalExitTime').val('');
                }

                $('#updateSignalNotes').val(signal.notes || '');

                // Hide signal detail modal and show update modal
                $('#signalDetailModal').modal('hide');
                $('#updateSignalModal').modal('show');
            },
            error: function(xhr) {
                alert('Error loading signal details: ' + xhr.responseJSON.detail);
            }
        });
    }

    function saveSignalUpdate() {
        const signalId = $('#updateSignalId').val();
        const formData = {
            status: $('#updateSignalStatus').val(),
            exit_price: $('#updateSignalExitPrice').val() ? parseFloat($('#updateSignalExitPrice').val()) : null,
            exit_time: $('#updateSignalExitTime').val() || null,
            notes: $('#updateSignalNotes').val()
        };

        $.ajax({
            url: `/api/signals/${signalId}`,
            type: 'PUT',
            contentType: 'application/json',
            data: JSON.stringify(formData),
            success: function(response) {
                alert('Signal updated successfully!');
                $('#updateSignalModal').modal('hide');
                loadSignals();
            },
            error: function(xhr) {
                alert('Error updating signal: ' + xhr.responseJSON.detail);
            }
        });
    }

    function generateSignalPost(signalId) {
        // Get available channels first
        $.ajax({
            url: '/api/channels',
            type: 'GET',
            success: function(channels) {
                if (channels.length === 0) {
                    alert('No active channels found. Please create and activate a channel first.');
                    return;
                }

                // If only one channel, use it directly
                if (channels.length === 1) {
                    generateSignalPostForChannel(signalId, channels[0].id);
                    return;
                }

                // Show channel selection modal
                let html = `
                    <div class="modal fade" id="signalChannelSelectModal" tabindex="-1">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Select Channel</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="mb-3">
                                        <label for="signalChannelSelect" class="form-label">Choose a channel to generate the signal post:</label>
                                        <select class="form-select" id="signalChannelSelect">
                `;

                channels.forEach(channel => {
                    html += `<option value="${channel.id}">${channel.name} (${channel.language})</option>`;
                });

                html += `
                                        </select>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                    <button type="button" class="btn btn-primary" onclick="generateSignalPostForChannel(${signalId}, $('#signalChannelSelect').val())">Generate Post</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Remove existing modal if any
                $('#signalChannelSelectModal').remove();
                $('body').append(html);
                $('#signalChannelSelectModal').modal('show');
            },
            error: function() {
                alert('Error loading channels');
            }
        });
    }

    function generateSignalPostForChannel(signalId, channelId) {
        $('#signalChannelSelectModal').modal('hide');

        $.ajax({
            url: '/api/signals/generate-post',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ signal_id: signalId, channel_id: parseInt(channelId) }),
            success: function(response) {
                alert('Post generated successfully!');
                // Redirect to posts page
                window.location.href = '/posts';
            },
            error: function(xhr) {
                alert('Error generating post: ' + xhr.responseJSON.detail);
            }
        });
    }

    function generateSignalUpdate(signalId) {
        // Get available channels first
        $.ajax({
            url: '/api/channels',
            type: 'GET',
            success: function(channels) {
                if (channels.length === 0) {
                    alert('No active channels found. Please create and activate a channel first.');
                    return;
                }

                // If only one channel, use it directly
                if (channels.length === 1) {
                    generateSignalUpdateForChannel(signalId, channels[0].id);
                    return;
                }

                // Show channel selection modal
                let html = `
                    <div class="modal fade" id="signalUpdateChannelSelectModal" tabindex="-1">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Select Channel</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="mb-3">
                                        <label for="signalUpdateChannelSelect" class="form-label">Choose a channel to generate the performance update:</label>
                                        <select class="form-select" id="signalUpdateChannelSelect">
                `;

                channels.forEach(channel => {
                    html += `<option value="${channel.id}">${channel.name} (${channel.language})</option>`;
                });

                html += `
                                        </select>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                    <button type="button" class="btn btn-primary" onclick="generateSignalUpdateForChannel(${signalId}, $('#signalUpdateChannelSelect').val())">Generate Update</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Remove existing modal if any
                $('#signalUpdateChannelSelectModal').remove();
                $('body').append(html);
                $('#signalUpdateChannelSelectModal').modal('show');
            },
            error: function() {
                alert('Error loading channels');
            }
        });
    }

    function generateSignalUpdateForChannel(signalId, channelId) {
        $('#signalUpdateChannelSelectModal').modal('hide');

        $.ajax({
            url: '/api/signals/generate-update',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ signal_id: signalId, channel_id: parseInt(channelId) }),
            success: function(response) {
                alert('Performance update post generated successfully!');
                // Redirect to posts page
                window.location.href = '/posts';
            },
            error: function(xhr) {
                alert('Error generating performance update: ' + xhr.responseJSON.detail);
            }
        });
    }

    function processStrategies() {
        $.ajax({
            url: '/api/actions/process-strategies',
            type: 'POST',
            success: function(response) {
                alert('Strategy processing started successfully!');
            },
            error: function(xhr) {
                alert('Error: ' + xhr.responseJSON.detail);
            }
        });
    }

    function deleteSignal(signalId) {
        if (!confirm('Are you sure you want to delete this signal? This action cannot be undone.')) {
            return;
        }

        $.ajax({
            url: `/api/signals/${signalId}`,
            type: 'DELETE',
            success: function(response) {
                alert('Signal deleted successfully!');
                loadSignals(); // Reload the signals table
            },
            error: function(xhr) {
                if (xhr.responseJSON && xhr.responseJSON.detail) {
                    alert('Error deleting signal: ' + xhr.responseJSON.detail);
                } else {
                    alert('Error deleting signal. Please try again.');
                }
            }
        });
    }
</script>
{% endblock %}
