#!/usr/bin/env python3
"""
Script to backup strategies, prompt templates, and channel configurations from MignalyBot database

This script will:
1. Connect to the database
2. Export strategies, prompt templates, and channel configs to JSON files
3. Create a timestamped backup directory
4. Optionally create a single consolidated backup file

Usage:
    python scripts/backup_configurations.py [--output-dir DIR] [--single-file] [--format json|sql]

Options:
    --output-dir DIR    Directory to save backup files (default: backups/TIMESTAMP)
    --single-file       Create a single consolidated backup file
    --format FORMAT     Backup format: json or sql (default: json)
"""

import os
import sys
import json
import argparse
import logging
from datetime import datetime
from pathlib import Path

# Add the src directory to the Python path
script_dir = Path(__file__).parent
project_root = script_dir.parent
src_dir = project_root / "src"
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(src_dir))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def setup_environment():
    """Setup environment variables from .env file"""
    env_file = project_root / ".env"
    if env_file.exists():
        from dotenv import load_dotenv
        load_dotenv(env_file)
        logger.info(f"Loaded environment from {env_file}")
    else:
        logger.warning(f"No .env file found at {env_file}")

def get_database_session():
    """Get database session and models"""
    try:
        from src.database.setup import SessionLocal
        from src.database.models import Strategy, PromptTemplate, Channel
        
        session = SessionLocal()
        return session, Strategy, PromptTemplate, Channel
    except ImportError as e:
        logger.error(f"Failed to import database modules: {e}")
        sys.exit(1)

def serialize_model(obj):
    """Convert SQLAlchemy model to dictionary"""
    result = {}
    for column in obj.__table__.columns:
        value = getattr(obj, column.name)
        if isinstance(value, datetime):
            value = value.isoformat()
        result[column.name] = value
    return result

def backup_strategies(session, Strategy):
    """Backup all strategies"""
    try:
        strategies = session.query(Strategy).all()
        strategies_data = [serialize_model(strategy) for strategy in strategies]
        
        logger.info(f"Backed up {len(strategies_data)} strategies")
        return strategies_data
    except Exception as e:
        logger.error(f"Failed to backup strategies: {e}")
        return []

def backup_prompt_templates(session, PromptTemplate):
    """Backup all prompt templates"""
    try:
        templates = session.query(PromptTemplate).all()
        templates_data = [serialize_model(template) for template in templates]
        
        logger.info(f"Backed up {len(templates_data)} prompt templates")
        return templates_data
    except Exception as e:
        logger.error(f"Failed to backup prompt templates: {e}")
        return []

def backup_channels(session, Channel):
    """Backup all channel configurations"""
    try:
        channels = session.query(Channel).all()
        channels_data = [serialize_model(channel) for channel in channels]
        
        logger.info(f"Backed up {len(channels_data)} channel configurations")
        return channels_data
    except Exception as e:
        logger.error(f"Failed to backup channels: {e}")
        return []

def create_backup_directory(output_dir=None):
    """Create backup directory with timestamp"""
    if output_dir is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_dir = project_root / "backups" / f"config_backup_{timestamp}"
    else:
        backup_dir = Path(output_dir)
    
    backup_dir.mkdir(parents=True, exist_ok=True)
    logger.info(f"Created backup directory: {backup_dir}")
    return backup_dir

def save_json_backup(data, filepath):
    """Save data as JSON file"""
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        logger.info(f"Saved backup to: {filepath}")
        return True
    except Exception as e:
        logger.error(f"Failed to save backup to {filepath}: {e}")
        return False

def create_consolidated_backup(strategies, templates, channels, backup_dir):
    """Create a single consolidated backup file"""
    consolidated_data = {
        "backup_info": {
            "timestamp": datetime.now().isoformat(),
            "version": "1.0",
            "description": "MignalyBot configuration backup"
        },
        "strategies": strategies,
        "prompt_templates": templates,
        "channels": channels,
        "statistics": {
            "strategies_count": len(strategies),
            "templates_count": len(templates),
            "channels_count": len(channels)
        }
    }
    
    consolidated_file = backup_dir / "consolidated_backup.json"
    return save_json_backup(consolidated_data, consolidated_file)

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Backup MignalyBot configurations")
    parser.add_argument("--output-dir", type=str, 
                       help="Directory to save backup files")
    parser.add_argument("--single-file", action="store_true",
                       help="Create a single consolidated backup file")
    parser.add_argument("--format", choices=["json", "sql"], default="json",
                       help="Backup format (default: json)")
    
    args = parser.parse_args()
    
    if args.format == "sql":
        logger.error("SQL format not yet implemented. Please use JSON format.")
        sys.exit(1)
    
    logger.info("Starting configuration backup...")
    
    # Setup environment
    setup_environment()
    
    # Get database session
    session, Strategy, PromptTemplate, Channel = get_database_session()
    
    try:
        # Create backup directory
        backup_dir = create_backup_directory(args.output_dir)
        
        # Backup each configuration type
        logger.info("Backing up strategies...")
        strategies = backup_strategies(session, Strategy)
        
        logger.info("Backing up prompt templates...")
        templates = backup_prompt_templates(session, PromptTemplate)
        
        logger.info("Backing up channel configurations...")
        channels = backup_channels(session, Channel)
        
        # Save individual backup files
        if not args.single_file:
            save_json_backup(strategies, backup_dir / "strategies.json")
            save_json_backup(templates, backup_dir / "prompt_templates.json")
            save_json_backup(channels, backup_dir / "channels.json")
        
        # Create consolidated backup if requested
        if args.single_file:
            create_consolidated_backup(strategies, templates, channels, backup_dir)
        
        # Create summary
        summary = {
            "backup_completed": datetime.now().isoformat(),
            "backup_directory": str(backup_dir),
            "items_backed_up": {
                "strategies": len(strategies),
                "prompt_templates": len(templates),
                "channels": len(channels)
            }
        }
        save_json_backup(summary, backup_dir / "backup_summary.json")
        
        logger.info("✅ Configuration backup completed successfully!")
        logger.info(f"Backup location: {backup_dir}")
        logger.info(f"Total items backed up: {len(strategies) + len(templates) + len(channels)}")
        
    except KeyboardInterrupt:
        logger.info("\nBackup cancelled by user (Ctrl+C)")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Backup failed: {e}")
        sys.exit(1)
    finally:
        session.close()

if __name__ == "__main__":
    main()
