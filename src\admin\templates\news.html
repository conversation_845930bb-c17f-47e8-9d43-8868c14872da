{% extends "base.html" %}

{% block title %}News - MignalyBot Admin{% endblock %}

{% block page_title %}News Management{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Financial News</h5>
        <div>
            <button type="button" class="btn btn-primary" id="refreshNewsBtn">
                <i class="fas fa-sync-alt"></i> Refresh
            </button>
            <button type="button" class="btn btn-success" id="collectNewsBtn">
                <i class="fas fa-download"></i> Collect News
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="mb-3">
            <div class="row">
                <div class="col-md-4">
                    <input type="text" class="form-control" id="searchNews" placeholder="Search news...">
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="sourceFilter">
                        <option value="">All Sources</option>
                        <!-- Sources will be populated dynamically -->
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="symbolFilter">
                        <option value="">All Symbols</option>
                        <!-- Symbols will be populated dynamically -->
                    </select>
                </div>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-hover" id="newsTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Title</th>
                        <th>Source</th>
                        <th>Symbols</th>
                        <th>Published</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="6" class="text-center">Loading news...</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="d-flex justify-content-between align-items-center mt-3">
            <div>
                <span id="newsCount">0</span> news items
            </div>
            <div>
                <nav aria-label="News pagination">
                    <ul class="pagination" id="newsPagination">
                        <!-- Pagination will be populated dynamically -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- News Detail Modal -->
<div class="modal fade" id="newsDetailModal" tabindex="-1" aria-labelledby="newsDetailModalTitle" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="newsDetailModalTitle">News Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <h5 id="newsTitle"></h5>
                    <div class="d-flex justify-content-between text-muted small">
                        <div>Source: <span id="newsSource"></span></div>
                        <div>Published: <span id="newsPublishedAt"></span></div>
                    </div>
                </div>

                <div class="mb-3">
                    <div id="newsImage" class="text-center mb-3"></div>
                    <div id="newsContent"></div>
                </div>

                <div class="mb-3">
                    <h6>AI Analysis</h6>
                    <div id="newsAnalysis" class="p-3 bg-light rounded"></div>
                </div>

                <div class="mb-3">
                    <h6>Related Symbols</h6>
                    <div id="newsSymbols"></div>
                </div>

                <div class="mb-3">
                    <a id="newsUrl" href="#" target="_blank" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-external-link-alt"></i> View Original
                    </a>
                    <button id="generatePostBtn" class="btn btn-sm btn-success">
                        <i class="fas fa-magic"></i> Generate Post
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let currentPage = 1;
    const pageSize = 20;
    let totalNews = 0;

    $(document).ready(function() {
        // Load news
        loadNews();

        // Set up event handlers
        $('#refreshNewsBtn').click(function() {
            loadNews();
        });

        $('#collectNewsBtn').click(function() {
            collectNews();
        });

        $('#searchNews, #sourceFilter, #symbolFilter').on('change keyup', function() {
            currentPage = 1;
            loadNews();
        });

        // Initialize tooltips
        $('[data-bs-toggle="tooltip"]').tooltip();
    });

    function loadNews() {
        const search = $('#searchNews').val();
        const source = $('#sourceFilter').val();
        const symbol = $('#symbolFilter').val();

        $.ajax({
            url: '/api/news',
            type: 'GET',
            data: {
                search: search,
                source: source,
                symbol: symbol,
                page: currentPage,
                page_size: pageSize
            },
            success: function(data) {
                totalNews = data.total;
                $('#newsCount').text(data.total);

                let tableHtml = '';

                if (data.news && data.news.length > 0) {
                    data.news.forEach(function(news) {
                        let publishedDate = 'N/A';
                        try {
                            // Check if published_time exists and is valid
                            if (news.published_time) {
                                publishedDate = new Date(news.published_time).toLocaleString();
                                // Check if the date is valid
                                if (publishedDate === 'Invalid Date') {
                                    publishedDate = 'N/A';
                                }
                            }
                        } catch (e) {
                            console.error('Error formatting date:', e);
                            publishedDate = 'N/A';
                        }

                        const symbols = news.symbols ? news.symbols : 'N/A';

                        tableHtml += `
                            <tr>
                                <td>${news.id}</td>
                                <td>
                                    <a href="#" class="news-detail-link" data-news-id="${news.id}">
                                        ${news.title}
                                    </a>
                                </td>
                                <td>${news.source}</td>
                                <td>${symbols}</td>
                                <td>${publishedDate}</td>
                                <td>
                                    <button class="btn btn-sm btn-primary news-detail-btn" data-news-id="${news.id}">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-success generate-post-btn" data-news-id="${news.id}">
                                        <i class="fas fa-magic"></i>
                                    </button>
                                </td>
                            </tr>
                        `;
                    });
                } else {
                    tableHtml = '<tr><td colspan="6" class="text-center">No news found</td></tr>';
                }

                $('#newsTable tbody').html(tableHtml);

                // Set up pagination
                updatePagination(data.total);

                // Set up event handlers for the newly created elements
                $('.news-detail-link, .news-detail-btn').click(function() {
                    const newsId = $(this).data('news-id');
                    showNewsDetail(newsId);
                });

                $('.generate-post-btn').click(function() {
                    const newsId = $(this).data('news-id');
                    generatePost(newsId);
                });

                // Populate source filter if it's empty
                if ($('#sourceFilter option').length <= 1) {
                    const sources = [...new Set(data.news.map(item => item.source))];
                    let sourceOptions = '<option value="">All Sources</option>';
                    sources.forEach(source => {
                        sourceOptions += `<option value="${source}">${source}</option>`;
                    });
                    $('#sourceFilter').html(sourceOptions);
                }

                // Populate symbol filter if it's empty
                if ($('#symbolFilter option').length <= 1) {
                    const allSymbols = data.news
                        .filter(item => item.symbols)
                        .flatMap(item => item.symbols.split(','));
                    const symbols = [...new Set(allSymbols)];
                    let symbolOptions = '<option value="">All Symbols</option>';
                    symbols.forEach(symbol => {
                        symbolOptions += `<option value="${symbol}">${symbol}</option>`;
                    });
                    $('#symbolFilter').html(symbolOptions);
                }
            },
            error: function(xhr) {
                $('#newsTable tbody').html('<tr><td colspan="6" class="text-center text-danger">Error loading news</td></tr>');
                console.error('Error loading news:', xhr);
            }
        });
    }

    function updatePagination(total) {
        const totalPages = Math.ceil(total / pageSize);
        let paginationHtml = '';

        if (totalPages > 1) {
            // Previous button
            paginationHtml += `
                <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="${currentPage - 1}">Previous</a>
                </li>
            `;

            // Page numbers
            for (let i = 1; i <= totalPages; i++) {
                if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                    paginationHtml += `
                        <li class="page-item ${i === currentPage ? 'active' : ''}">
                            <a class="page-link" href="#" data-page="${i}">${i}</a>
                        </li>
                    `;
                } else if (i === currentPage - 3 || i === currentPage + 3) {
                    paginationHtml += `
                        <li class="page-item disabled">
                            <a class="page-link" href="#">...</a>
                        </li>
                    `;
                }
            }

            // Next button
            paginationHtml += `
                <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="${currentPage + 1}">Next</a>
                </li>
            `;
        }

        $('#newsPagination').html(paginationHtml);

        // Set up event handlers for pagination
        $('.page-link').click(function(e) {
            e.preventDefault();
            const page = $(this).data('page');
            if (page && page !== currentPage && page > 0 && page <= totalPages) {
                currentPage = page;
                loadNews();
            }
        });
    }

    function showNewsDetail(newsId) {
        $.ajax({
            url: `/api/news/${newsId}`,
            type: 'GET',
            success: function(news) {
                $('#newsTitle').text(news.title);
                $('#newsSource').text(news.source);

                // Handle published date
                let publishedDate = 'N/A';
                try {
                    if (news.published_time) {
                        publishedDate = new Date(news.published_time).toLocaleString();
                        if (publishedDate === 'Invalid Date') {
                            publishedDate = 'N/A';
                        }
                    }
                } catch (e) {
                    console.error('Error formatting date:', e);
                }
                $('#newsPublishedAt').text(publishedDate);

                $('#newsContent').html(news.content);
                $('#newsAnalysis').html(news.ai_analysis || '<em>No AI analysis available</em>');
                $('#newsSymbols').text(news.symbols || 'N/A');
                $('#newsUrl').attr('href', news.url);

                // Set image if available
                if (news.image_url) {
                    $('#newsImage').html(`<img src="${news.image_url}" class="img-fluid rounded" alt="${news.title}">`);
                } else {
                    $('#newsImage').empty();
                }

                // Set up generate post button
                $('#generatePostBtn').data('news-id', news.id);

                // Show modal
                $('#newsDetailModal').modal('show');
            },
            error: function(xhr) {
                alert('Error loading news details: ' + xhr.responseJSON.detail);
            }
        });
    }

    function generatePost(newsId) {
        // Get available channels first
        $.ajax({
            url: '/api/channels',
            type: 'GET',
            success: function(channels) {
                if (channels.length === 0) {
                    alert('No active channels found. Please create and activate a channel first.');
                    return;
                }

                // If only one channel, use it directly
                if (channels.length === 1) {
                    generatePostForChannel(newsId, channels[0].id);
                    return;
                }

                // Show channel selection dialog
                let channelOptions = channels.map(ch => `<option value="${ch.id}">${ch.name}</option>`).join('');
                let html = `
                    <div class="modal fade" id="channelSelectModal" tabindex="-1">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Select Channel</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <label for="channelSelect" class="form-label">Choose a channel to generate the post for:</label>
                                    <select class="form-select" id="channelSelect">
                                        ${channelOptions}
                                    </select>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                    <button type="button" class="btn btn-primary" onclick="generatePostForChannel(${newsId}, $('#channelSelect').val())">Generate Post</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Remove existing modal if any
                $('#channelSelectModal').remove();
                $('body').append(html);
                $('#channelSelectModal').modal('show');
            },
            error: function() {
                alert('Error loading channels');
            }
        });
    }

    function generatePostForChannel(newsId, channelId) {
        $('#channelSelectModal').modal('hide');

        $.ajax({
            url: '/api/news/generate-post',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ news_id: newsId, channel_id: parseInt(channelId) }),
            success: function(response) {
                alert('Post generated successfully!');
                // Redirect to posts page
                window.location.href = '/posts';
            },
            error: function(xhr) {
                alert('Error generating post: ' + xhr.responseJSON.detail);
            }
        });
    }

    function collectNews() {
        $.ajax({
            url: '/api/actions/collect-news',
            type: 'POST',
            success: function(response) {
                alert('News collection started successfully!');
            },
            error: function(xhr) {
                alert('Error: ' + xhr.responseJSON.detail);
            }
        });
    }
</script>
{% endblock %}
